On Error Resume Next

Set WshShell = CreateObject("WScript.Shell")
CurrentDirectory = CreateObject("Scripting.FileSystemObject").GetParentFolderName(WScript.ScriptFullName)
WshShell.CurrentDirectory = CurrentDirectory

' تشغيل البرنامج مباشرة
WshShell.Run "cmd /c python app/main.py", 0, False

' إذا حدث خطأ، عرض رسالة وتشغيل البرنامج مع إظهار نافذة CMD
If Err.Number <> 0 Then
    MsgBox "حدث خطأ أثناء تشغيل البرنامج. سيتم تشغيل البرنامج مع إظهار نافذة CMD لمعرفة الخطأ.", vbExclamation, "تنبيه"
    WshShell.Run "cmd /c python app/main.py & pause", 1, True
End If