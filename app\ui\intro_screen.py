#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Intro Screen for Employee Salary Management System
Displays an animated intro with program information
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
import math
import random  # إضافة مكتبة random للأرقام العشوائية

class IntroScreen:
    """Animated intro screen with program information"""
    
    def __init__(self, parent, callback=None):
        """Initialize the intro screen"""
        self.parent = parent
        self.callback = callback
        
        # Create window
        self.window = tk.Toplevel(parent)
        self.window.title("نظام إدارة رواتب الموظفين")
        # Get screen width and height
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        # Choose between overrideredirect or fullscreen (can't use both)
        # Using overrideredirect for a cleaner look
        self.window.overrideredirect(True)  # Remove window decorations
        self.window.geometry(f"{screen_width}x{screen_height}")
        self.window.resizable(True, True)
        
        # Make window appear on top
        self.window.attributes('-topmost', True)
        
        # Center the window on screen
        self.window.update_idletasks()
        x = 0
        y = 0
        self.window.geometry(f"{screen_width}x{screen_height}+{x}+{y}")
        
        # Create canvas for animations with blue background
        self.canvas = tk.Canvas(self.window, width=screen_width, height=screen_height, bg='#0000FF', highlightthickness=0)  # Azul brillante
        self.canvas.pack(fill=tk.BOTH, expand=True)
        
        # Animation variables
        self.animation_running = True
        self.wave_offset = 0
        self.text_alpha = 0
        self.particles = []
        
        # Create particles for fullscreen
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        
        # More particles for larger screen
        particle_count = 100
        
        for _ in range(particle_count):
            x = random.randint(0, screen_width)
            y = random.randint(0, screen_height)
            size = random.randint(2, 6)
            speed = random.randint(1, 4) / 10
            color_idx = random.randint(0, 2)
            colors = ["#FF00FF", "#8A2BE2", "#9400D3"]  # Púrpura brillante, Violeta azulado, Violeta oscuro
            self.particles.append({
                'x': x, 'y': y, 'size': size, 'speed': speed, 
                'color': colors[color_idx], 'angle': random.randint(0, 360)
            })
        
        # Start animation
        self.animate()
        
        # Set timer to close intro after 5 seconds
        self.window.after(5000, self.close_intro)
        
    def center_window(self):
        """Center the window on the screen"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
    def animate(self):
        """Animate the intro screen"""
        if not self.animation_running:
            return
            
        # Clear canvas
        self.canvas.delete("all")
        
        # Draw animated background
        self.draw_wave_background()
        
        # Draw particles
        self.update_particles()
        
        # Draw program title with animation
        self.draw_title()
        
        # Draw developer info
        self.draw_developer_info()
        
        # Draw version info
        screen_width = self.window.winfo_width()
        screen_height = self.window.winfo_height()
        screen_center_x = screen_width // 2
        
        self.canvas.create_text(
            screen_center_x, screen_height - 50, 
            text="الإصدار v2.0", 
            fill="#FFFFFF", 
            font=("Arial", 16, "bold"),
            tags="version"
        )
        
        # Continue animation
        self.window.after(30, self.animate)
        
    def draw_wave_background(self):
        """Draw animated background with blue and purple colors (without snake-like waves)"""
        # Update wave offset for other animations
        self.wave_offset += 0.05
        
        # Instead of drawing snake-like waves, create a gradient background
        screen_width = self.window.winfo_width()
        screen_height = self.window.winfo_height()
        
        # Create gradient effect with blue and purple
        gradient_steps = 20
        for i in range(gradient_steps):
            # Calculate color based on position
            ratio = i / gradient_steps
            
            # Interpolate between blue and purple
            r = int(0 + (148 * ratio))  # From blue to purple
            g = int(0 + (0 * ratio))
            b = int(255 + (-44 * ratio))
            
            color = f"#{r:02x}{g:02x}{b:02x}"
            
            # Calculate position
            y1 = int(screen_height * (i / gradient_steps))
            y2 = int(screen_height * ((i + 1) / gradient_steps))
            
            # Draw rectangle for this gradient step
            self.canvas.create_rectangle(
                0, y1, screen_width, y2,
                fill=color, outline=""
            )
        
    def adjust_color_alpha(self, color, alpha):
        """Adjust color transparency"""
        # Convert hex to RGB
        r = int(color[1:3], 16)
        g = int(color[3:5], 16)
        b = int(color[5:7], 16)
        
        # Adjust alpha
        r = int(r * alpha)
        g = int(g * alpha)
        b = int(b * alpha)
        
        # Convert back to hex
        return f"#{r:02x}{g:02x}{b:02x}"
        
    def update_particles(self):
        """Update and draw particles"""
        screen_width = self.window.winfo_width()
        screen_height = self.window.winfo_height()
        
        for particle in self.particles:
            # Update position
            particle['x'] += math.cos(particle['angle']) * particle['speed']
            particle['y'] += math.sin(particle['angle']) * particle['speed']
            
            # Wrap around screen
            if particle['x'] < 0:
                particle['x'] = screen_width
            elif particle['x'] > screen_width:
                particle['x'] = 0
                
            if particle['y'] < 0:
                particle['y'] = screen_height
            elif particle['y'] > screen_height:
                particle['y'] = 0
                
            # Draw particle
            self.canvas.create_oval(
                particle['x'] - particle['size'], 
                particle['y'] - particle['size'],
                particle['x'] + particle['size'], 
                particle['y'] + particle['size'],
                fill=particle['color'], 
                outline=""
            )
            
    def draw_title(self):
        """Draw program title with animation"""
        # Update text alpha for fade-in effect
        if self.text_alpha < 1.0:
            self.text_alpha += 0.05
            
        # Get screen center
        screen_width = self.window.winfo_width()
        screen_center_x = screen_width // 2
            
        # Main title with yellow color
        title_color = self.adjust_color_alpha("#FFFF00", self.text_alpha)  # Amarillo brillante
        self.canvas.create_text(
            screen_center_x, 150, 
            text="نظام إدارة رواتب الموظفين", 
            fill=title_color, 
            font=("Arial", 42, "bold"),
            tags="title"
        )
            
        # Developer name (simple, without 3D effect)
        self.canvas.create_text(
            screen_center_x, 250, 
            text="المحاسب المبرمج : علي عاجل خشان المحنة", 
            fill="#FFFFFF", 
            font=("Arial", 28, "bold"),
            tags="developer"
        )
        
    def draw_developer_info(self):
        """Draw copyright information"""
        copyright_text = "جميع الحقوق محفوظة © 2023"
        
        # Get screen center
        screen_width = self.window.winfo_width()
        screen_height = self.window.winfo_height()
        screen_center_x = screen_width // 2
        
        # Calculate pulsing effect
        pulse = (math.sin(self.wave_offset * 2) + 1) / 2  # Value between 0 and 1
        
        # Interpolate between colors based on pulse
        r = int(138 + (255 - 138) * pulse)  # From purple to white
        g = int(43 + (255 - 43) * pulse)
        b = int(226 + (255 - 226) * pulse)
        
        color = f"#{r:02x}{g:02x}{b:02x}"
        
        self.canvas.create_text(
            screen_center_x, screen_height - 100, 
            text=copyright_text, 
            fill=color, 
            font=("Arial", 18),
            tags="copyright"
        )
        
    def close_intro(self):
        """Close the intro screen and call callback"""
        self.animation_running = False
        self.window.destroy()
        
        if self.callback:
            self.callback()
            
def show_intro(parent, callback=None):
    """Show the intro screen"""
    intro = IntroScreen(parent, callback)
    return intro