@echo off
echo ===== تشخيص نظام إدارة رواتب الموظفين =====
echo.
echo التحقق من وجود Python...
where python
if %ERRORLEVEL% NEQ 0 (
    echo Python غير موجود في النظام أو غير مضاف إلى متغير PATH
    echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
    echo تأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت
) else (
    echo تم العثور على Python
    python --version
)

echo.
echo التحقق من وجود الملفات الضرورية...
if exist app\main.py (
    echo تم العثور على ملف main.py
) else (
    echo ملف main.py غير موجود!
)

echo.
echo محاولة تشغيل البرنامج مباشرة...
python app/main.py

echo.
echo انتهى التشخيص
pause