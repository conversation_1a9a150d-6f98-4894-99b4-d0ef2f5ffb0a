@echo off
echo تشغيل نظام إدارة رواتب الموظفين مباشرة...
echo.

REM تشغيل البرنامج مباشرة
cd /d "%~dp0"
python app/main.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo حدث خطأ أثناء تشغيل البرنامج
    echo.
    echo التحقق من وجود Python...
    where python
    if %ERRORLEVEL% NEQ 0 (
        echo Python غير موجود في النظام أو غير مضاف إلى متغير PATH
        echo يرجى تشغيل ملف تثبيت_Python.bat أولاً
    ) else (
        echo تم العثور على Python:
        python --version
        echo.
        echo التحقق من المكتبات المطلوبة...
        python -c "import tkinter; print('tkinter موجود')" 2>nul
        if %ERRORLEVEL% NEQ 0 echo tkinter غير موجود
        python -c "import pandas; print('pandas موجود')" 2>nul
        if %ERRORLEVEL% NEQ 0 echo pandas غير موجود
        python -c "import matplotlib; print('matplotlib موجود')" 2>nul
        if %ERRORLEVEL% NEQ 0 echo matplotlib غير موجود
        echo.
        echo يرجى تشغيل ملف install_dependencies.bat لتثبيت المكتبات المطلوبة
    )
)

pause