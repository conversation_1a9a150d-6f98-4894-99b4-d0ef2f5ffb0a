#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Database Manager for Employee Salary Management System
Handles all database operations using SQLite
"""

import os
import sqlite3
import json
from datetime import datetime

class DatabaseManager:
    """Manages all database operations for the application"""
    
    def __init__(self, db_path=None):
        """Initialize the database connection"""
        if db_path is None:
            # Use default path in the app directory
            app_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            db_dir = os.path.join(app_dir, 'database')
            os.makedirs(db_dir, exist_ok=True)
            db_path = os.path.join(db_dir, 'employee_salaries.db')
            
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        self.connect()
        self.create_tables()
        
    def connect(self):
        """Connect to the SQLite database"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row  # Return rows as dictionaries
            self.cursor = self.conn.cursor()
            return True
        except sqlite3.Error as e:
            print(f"Database connection error: {e}")
            return False
            
    def close(self):
        """Close the database connection"""
        if self.conn:
            self.conn.close()
            
    def create_tables(self):
        """Create the necessary tables if they don't exist"""
        try:
            # Departments table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS departments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # Employees table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS employees (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                job_title TEXT,
                department_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (department_id) REFERENCES departments (id)
            )
            ''')
            
            # Salary records table
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS salary_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                employee_id INTEGER NOT NULL,
                year INTEGER NOT NULL,
                month INTEGER NOT NULL,
                prim_salary_c REAL DEFAULT 0,
                zawjiya REAL DEFAULT 0,
                athfal REAL DEFAULT 0,
                denger REAL DEFAULT 0,
                mansib_c REAL DEFAULT 0,
                shahada REAL DEFAULT 0,
                nakil REAL DEFAULT 0,
                handasiya REAL DEFAULT 0,
                arzaak REAL DEFAULT 0,
                incom_gc REAL DEFAULT 0,
                tokifat REAL DEFAULT 0,
                dariba REAL DEFAULT 0,
                eikari REAL DEFAULT 0,
                reaya REAL DEFAULT 0,
                raseem REAL DEFAULT 0,
                tanfeeth REAL DEFAULT 0,
                eskan REAL DEFAULT 0,
                rasheed REAL DEFAULT 0,
                rafidaen REAL DEFAULT 0,
                eijar REAL DEFAULT 0,
                amanat_okra REAL DEFAULT 0,
                tin REAL DEFAULT 0,
                tout REAL DEFAULT 0,
                alsafi REAL DEFAULT 0,
                note TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (employee_id) REFERENCES employees (id),
                UNIQUE (employee_id, year, month)
            )
            ''')
            
            # Insert default departments
            default_departments = [
                "الرافدين المجموعة أ",
                "الرافدين قضاء الشامية",
                "الرافدين قضاء غماس",
                "الرافدين قضاء المهناوية"
            ]
            
            for dept in default_departments:
                self.cursor.execute(
                    "INSERT OR IGNORE INTO departments (name) VALUES (?)",
                    (dept,)
                )
                
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            print(f"Error creating tables: {e}")
            return False
            
    # Department operations
    def add_department(self, name):
        """Add a new department"""
        try:
            self.cursor.execute(
                "INSERT INTO departments (name) VALUES (?)",
                (name,)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding department: {e}")
            return None
            
    def get_departments(self):
        """Get all departments"""
        try:
            self.cursor.execute("SELECT id, name FROM departments ORDER BY name")
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting departments: {e}")
            return []
            
    # Employee operations
    def add_employee(self, name, job_title, department_id):
        """Add a new employee"""
        try:
            self.cursor.execute(
                "INSERT INTO employees (name, job_title, department_id) VALUES (?, ?, ?)",
                (name, job_title, department_id)
            )
            self.conn.commit()
            return self.cursor.lastrowid
        except sqlite3.Error as e:
            print(f"Error adding employee: {e}")
            return None
            
    def get_employees(self, department_id=None):
        """Get all employees, optionally filtered by department"""
        try:
            if department_id:
                self.cursor.execute(
                    """SELECT e.id, e.name, e.job_title, e.department_id, d.name as department_name 
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    WHERE e.department_id = ?
                    ORDER BY e.name""",
                    (department_id,)
                )
            else:
                self.cursor.execute(
                    """SELECT e.id, e.name, e.job_title, e.department_id, d.name as department_name 
                    FROM employees e
                    JOIN departments d ON e.department_id = d.id
                    ORDER BY e.name"""
                )
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting employees: {e}")
            return []
            
    # Salary record operations
    def add_salary_record(self, employee_id, year, month, data):
        """Add or update a salary record for an employee"""
        try:
            # Calculate totals
            tin = (
                data.get('prim_salary_c', 0) +
                data.get('zawjiya', 0) +
                data.get('athfal', 0) +
                data.get('denger', 0) +
                data.get('mansib_c', 0) +
                data.get('shahada', 0) +
                data.get('nakil', 0) +
                data.get('handasiya', 0) +
                data.get('arzaak', 0) +
                data.get('incom_gc', 0)
            )
            
            tout = (
                data.get('tokifat', 0) +
                data.get('dariba', 0) +
                data.get('eikari', 0) +
                data.get('reaya', 0) +
                data.get('raseem', 0) +
                data.get('tanfeeth', 0) +
                data.get('eskan', 0) +
                data.get('rasheed', 0) +
                data.get('rafidaen', 0) +
                data.get('eijar', 0) +
                data.get('amanat_okra', 0)
            )
            
            alsafi = tin - tout
            
            # Update data with calculated values
            data['tin'] = tin
            data['tout'] = tout
            data['alsafi'] = alsafi
            
            # Check if record exists
            self.cursor.execute(
                "SELECT id FROM salary_records WHERE employee_id = ? AND year = ? AND month = ?",
                (employee_id, year, month)
            )
            existing_record = self.cursor.fetchone()
            
            if existing_record:
                # Update existing record
                query = """UPDATE salary_records SET 
                    prim_salary_c = ?, zawjiya = ?, athfal = ?, denger = ?, 
                    mansib_c = ?, shahada = ?, nakil = ?, handasiya = ?, 
                    arzaak = ?, incom_gc = ?, tokifat = ?, dariba = ?, 
                    eikari = ?, reaya = ?, raseem = ?, tanfeeth = ?, 
                    eskan = ?, rasheed = ?, rafidaen = ?, eijar = ?, 
                    amanat_okra = ?, tin = ?, tout = ?, alsafi = ?, note = ?
                    WHERE id = ?"""
                
                self.cursor.execute(query, (
                    data.get('prim_salary_c', 0),
                    data.get('zawjiya', 0),
                    data.get('athfal', 0),
                    data.get('denger', 0),
                    data.get('mansib_c', 0),
                    data.get('shahada', 0),
                    data.get('nakil', 0),
                    data.get('handasiya', 0),
                    data.get('arzaak', 0),
                    data.get('incom_gc', 0),
                    data.get('tokifat', 0),
                    data.get('dariba', 0),
                    data.get('eikari', 0),
                    data.get('reaya', 0),
                    data.get('raseem', 0),
                    data.get('tanfeeth', 0),
                    data.get('eskan', 0),
                    data.get('rasheed', 0),
                    data.get('rafidaen', 0),
                    data.get('eijar', 0),
                    data.get('amanat_okra', 0),
                    tin,
                    tout,
                    alsafi,
                    data.get('note', ''),
                    existing_record['id']
                ))
                record_id = existing_record['id']
            else:
                # Insert new record
                query = """INSERT INTO salary_records (
                    employee_id, year, month, prim_salary_c, zawjiya, athfal, 
                    denger, mansib_c, shahada, nakil, handasiya, arzaak, 
                    incom_gc, tokifat, dariba, eikari, reaya, raseem, 
                    tanfeeth, eskan, rasheed, rafidaen, eijar, amanat_okra, 
                    tin, tout, alsafi, note
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"""
                
                self.cursor.execute(query, (
                    employee_id, year, month,
                    data.get('prim_salary_c', 0),
                    data.get('zawjiya', 0),
                    data.get('athfal', 0),
                    data.get('denger', 0),
                    data.get('mansib_c', 0),
                    data.get('shahada', 0),
                    data.get('nakil', 0),
                    data.get('handasiya', 0),
                    data.get('arzaak', 0),
                    data.get('incom_gc', 0),
                    data.get('tokifat', 0),
                    data.get('dariba', 0),
                    data.get('eikari', 0),
                    data.get('reaya', 0),
                    data.get('raseem', 0),
                    data.get('tanfeeth', 0),
                    data.get('eskan', 0),
                    data.get('rasheed', 0),
                    data.get('rafidaen', 0),
                    data.get('eijar', 0),
                    data.get('amanat_okra', 0),
                    tin,
                    tout,
                    alsafi,
                    data.get('note', '')
                ))
                record_id = self.cursor.lastrowid
                
            self.conn.commit()
            return record_id
        except sqlite3.Error as e:
            print(f"Error adding salary record: {e}")
            return None
            
    def get_employee_salary_records(self, employee_id, year=None):
        """Get salary records for an employee, optionally filtered by year"""
        try:
            if year:
                self.cursor.execute(
                    """SELECT * FROM salary_records 
                    WHERE employee_id = ? AND year = ? 
                    ORDER BY year DESC, month DESC""",
                    (employee_id, year)
                )
            else:
                self.cursor.execute(
                    """SELECT * FROM salary_records 
                    WHERE employee_id = ? 
                    ORDER BY year DESC, month DESC""",
                    (employee_id,)
                )
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error getting salary records: {e}")
            return []
            
    def clear_salary_records_by_year(self, year):
        """Clear all salary records for a specific year"""
        try:
            self.cursor.execute(
                "DELETE FROM salary_records WHERE year = ?",
                (year,)
            )
            self.conn.commit()
            return self.cursor.rowcount
        except sqlite3.Error as e:
            print(f"Error clearing salary records for year {year}: {e}")
            return 0
        
    def clear_all_salary_records(self):
        """Clear all salary records from the database"""
        try:
            self.cursor.execute("DELETE FROM salary_records")
            self.conn.commit()
            return self.cursor.rowcount
        except sqlite3.Error as e:
            print(f"Error clearing all salary records: {e}")
            return 0
            
    def get_salary_record(self, employee_id, year, month):
        """Get a specific salary record"""
        try:
            self.cursor.execute(
                """SELECT * FROM salary_records 
                WHERE employee_id = ? AND year = ? AND month = ?""",
                (employee_id, year, month)
            )
            return self.cursor.fetchone()
        except sqlite3.Error as e:
            print(f"Error getting salary record: {e}")
            return None
            
    def search_salary_records(self, filters):
        """Search salary records with various filters"""
        try:
            query = """
            SELECT sr.*, e.name as employee_name, e.job_title, d.name as department_name
            FROM salary_records sr
            JOIN employees e ON sr.employee_id = e.id
            JOIN departments d ON e.department_id = d.id
            WHERE 1=1
            """
            params = []
            
            if filters.get('employee_id'):
                query += " AND sr.employee_id = ?"
                params.append(filters['employee_id'])
                
            if filters.get('department_id'):
                query += " AND e.department_id = ?"
                params.append(filters['department_id'])
                
            if filters.get('year'):
                query += " AND sr.year = ?"
                params.append(filters['year'])
                
            if filters.get('month'):
                query += " AND sr.month = ?"
                params.append(filters['month'])
                
            if filters.get('job_title'):
                query += " AND e.job_title LIKE ?"
                params.append(f"%{filters['job_title']}%")
                
            query += " ORDER BY sr.year DESC, sr.month DESC, e.name"
            
            self.cursor.execute(query, params)
            return self.cursor.fetchall()
        except sqlite3.Error as e:
            print(f"Error searching salary records: {e}")
            return []
            
    def export_to_json(self, path):
        """Export the entire database to a JSON file"""
        try:
            data = {
                'departments': [],
                'employees': [],
                'salary_records': []
            }
            
            # Get departments
            self.cursor.execute("SELECT * FROM departments")
            data['departments'] = [dict(row) for row in self.cursor.fetchall()]
            
            # Get employees
            self.cursor.execute("SELECT * FROM employees")
            data['employees'] = [dict(row) for row in self.cursor.fetchall()]
            
            # Get salary records
            self.cursor.execute("SELECT * FROM salary_records")
            data['salary_records'] = [dict(row) for row in self.cursor.fetchall()]
            
            with open(path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
                
            return True
        except Exception as e:
            print(f"Error exporting database: {e}")
            return False
            
    def import_from_json(self, path):
        """Import data from a JSON file"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # Begin transaction
            self.conn.execute("BEGIN TRANSACTION")
            
            # Import departments
            for dept in data.get('departments', []):
                self.cursor.execute(
                    "INSERT OR IGNORE INTO departments (id, name, created_at) VALUES (?, ?, ?)",
                    (dept.get('id'), dept.get('name'), dept.get('created_at'))
                )
                
            # Import employees
            for emp in data.get('employees', []):
                self.cursor.execute(
                    """INSERT OR IGNORE INTO employees 
                    (id, name, job_title, department_id, created_at) 
                    VALUES (?, ?, ?, ?, ?)""",
                    (
                        emp.get('id'),
                        emp.get('name'),
                        emp.get('job_title'),
                        emp.get('department_id'),
                        emp.get('created_at')
                    )
                )
                
            # Import salary records
            for record in data.get('salary_records', []):
                self.cursor.execute(
                    """INSERT OR REPLACE INTO salary_records 
                    (id, employee_id, year, month, prim_salary_c, zawjiya, athfal, 
                    denger, mansib_c, shahada, nakil, handasiya, arzaak, 
                    incom_gc, tokifat, dariba, eikari, reaya, raseem, 
                    tanfeeth, eskan, rasheed, rafidaen, eijar, amanat_okra, 
                    tin, tout, alsafi, note, created_at) 
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
                    (
                        record.get('id'),
                        record.get('employee_id'),
                        record.get('year'),
                        record.get('month'),
                        record.get('prim_salary_c'),
                        record.get('zawjiya'),
                        record.get('athfal'),
                        record.get('denger'),
                        record.get('mansib_c'),
                        record.get('shahada'),
                        record.get('nakil'),
                        record.get('handasiya'),
                        record.get('arzaak'),
                        record.get('incom_gc'),
                        record.get('tokifat'),
                        record.get('dariba'),
                        record.get('eikari'),
                        record.get('reaya'),
                        record.get('raseem'),
                        record.get('tanfeeth'),
                        record.get('eskan'),
                        record.get('rasheed'),
                        record.get('rafidaen'),
                        record.get('eijar'),
                        record.get('amanat_okra'),
                        record.get('tin'),
                        record.get('tout'),
                        record.get('alsafi'),
                        record.get('note'),
                        record.get('created_at')
                    )
                )
                
            # Commit transaction
            self.conn.commit()
            return True
        except Exception as e:
            # Rollback in case of error
            self.conn.rollback()
            print(f"Error importing database: {e}")
            return False