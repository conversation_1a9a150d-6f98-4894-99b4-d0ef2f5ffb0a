@echo off
echo إنشاء اختصار لنظام إدارة رواتب الموظفين على سطح المكتب...

set SCRIPT="%TEMP%\%RANDOM%-%RANDOM%-%RANDOM%-%RANDOM%.vbs"
echo Set oWS = WScript.CreateObject("WScript.Shell") >> %SCRIPT%
echo sLinkFile = "%USERPROFILE%\Desktop\نظام إدارة رواتب الموظفين.lnk" >> %SCRIPT%
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> %SCRIPT%
echo oLink.TargetPath = "%~dp0تشغيل_البرنامج.vbs" >> %SCRIPT%
echo oLink.WorkingDirectory = "%~dp0" >> %SCRIPT%
echo oLink.Description = "نظام إدارة رواتب الموظفين" >> %SCRIPT%
echo oLink.IconLocation = "%~dp0app\resources\app_icon.ico, 0" >> %SCRIPT%
echo oLink.Save >> %SCRIPT%
cscript /nologo %SCRIPT%
del %SCRIPT%

echo تم إنشاء الاختصار بنجاح!
pause