#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Employee Tab for Employee Salary Management System
Handles employee data entry and editing
"""

import tkinter as tk
from tkinter import ttk, messagebox
import datetime

class EmployeeTab:
    """Tab for entering and editing employee data"""
    
    def __init__(self, parent, db_manager):
        """Initialize the employee tab"""
        self.parent = parent
        self.db_manager = db_manager
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # Create scrollable frame
        self.canvas = tk.Canvas(self.frame)
        self.scrollbar = ttk.Scrollbar(self.frame, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)
        
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        self.canvas.pack(side="left", fill="both", expand=True)
        self.scrollbar.pack(side="right", fill="y")
        
        # Create form elements
        self.create_employee_form()
        
        # Initialize variables
        self.current_employee_id = None
        
    def create_employee_form(self):
        """Create the employee data entry form"""
        # Main form container with two columns
        form_frame = ttk.Frame(self.scrollable_frame)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Left column (employee info)
        left_frame = ttk.LabelFrame(form_frame, text="معلومات الموظف")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Right column (salary details)
        right_frame = ttk.LabelFrame(form_frame, text="تفاصيل الراتب")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Employee info section
        self.create_employee_info_section(left_frame)
        
        # Salary details section
        self.create_salary_details_section(right_frame)
        
        # Buttons section
        self.create_buttons_section(form_frame)
        
    def create_employee_info_section(self, parent):
        """Create the employee information section"""
        # Employee name
        ttk.Label(parent, text="اسم الموظف:").grid(row=0, column=1, sticky=tk.E, padx=5, pady=5)
        self.employee_name_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.employee_name_var, width=30).grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Job title
        ttk.Label(parent, text="العنوان الوظيفي:").grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)
        self.job_title_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.job_title_var, width=30).grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Department
        ttk.Label(parent, text="المجموعة:").grid(row=2, column=1, sticky=tk.E, padx=5, pady=5)
        self.department_var = tk.StringVar()
        self.department_combo = ttk.Combobox(parent, textvariable=self.department_var, width=27)
        self.department_combo.grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Load departments
        self.load_departments()
        
        # Add department button
        ttk.Button(
            parent, 
            text="إضافة مجموعة جديدة", 
            command=self.add_new_department
        ).grid(row=3, column=0, columnspan=2, padx=5, pady=5)
        
        # Year
        ttk.Label(parent, text="السنة:").grid(row=4, column=1, sticky=tk.E, padx=5, pady=5)
        self.year_var = tk.StringVar()
        current_year = datetime.datetime.now().year
        years = [str(year) for year in range(current_year - 5, current_year + 6)]
        self.year_combo = ttk.Combobox(parent, textvariable=self.year_var, values=years, width=27)
        self.year_combo.grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        self.year_var.set(str(current_year))
        
        # Month
        ttk.Label(parent, text="الشهر:").grid(row=5, column=1, sticky=tk.E, padx=5, pady=5)
        self.month_var = tk.StringVar()
        months = [
            "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
            "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
        ]
        self.month_combo = ttk.Combobox(parent, textvariable=self.month_var, values=months, width=27)
        self.month_combo.grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        current_month = datetime.datetime.now().month
        self.month_var.set(months[current_month - 1])
        
        # Notes
        ttk.Label(parent, text="الملاحظات:").grid(row=6, column=1, sticky=tk.NE, padx=5, pady=5)
        self.note_var = tk.StringVar()
        self.note_text = tk.Text(parent, width=30, height=5)
        self.note_text.grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Employee selection
        ttk.Label(parent, text="اختيار موظف موجود:").grid(row=7, column=1, sticky=tk.E, padx=5, pady=5)
        self.employee_selection_var = tk.StringVar()
        self.employee_combo = ttk.Combobox(parent, textvariable=self.employee_selection_var, width=27)
        self.employee_combo.grid(row=7, column=0, sticky=tk.W, padx=5, pady=5)
        self.employee_combo.bind("<<ComboboxSelected>>", self.on_employee_selected)
        
        # Load employees
        self.load_employees()
        
    def create_salary_details_section(self, parent):
        """Create the salary details section"""
        # Create two sub-frames for entitlements and deductions
        entitlements_frame = ttk.LabelFrame(parent, text="الاستحقاقات")
        entitlements_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        deductions_frame = ttk.LabelFrame(parent, text="الاستقطاعات")
        deductions_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        totals_frame = ttk.LabelFrame(parent, text="المجاميع")
        totals_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Entitlements
        self.entitlement_vars = {}
        entitlement_fields = [
            ("الراتب الاسمي", "prim_salary_c"),
            ("م. الزوجية", "zawjiya"),
            ("م. الأطفال", "athfal"),
            ("م. الخطورة", "denger"),
            ("المنصب", "mansib_c"),
            ("م. الشهادة", "shahada"),
            ("م. النقل", "nakil"),
            ("م. الهندسية", "handasiya"),
            ("م. المهنية", "arzaak"),
            ("م. الخدمة الجامعية", "incom_gc")
        ]
        
        for i, (label_text, field_name) in enumerate(entitlement_fields):
            row = i // 2
            col_start = 0 if i % 2 == 0 else 2
            
            ttk.Label(entitlements_frame, text=label_text + ":").grid(
                row=row, column=col_start + 1, sticky=tk.E, padx=5, pady=5
            )
            
            self.entitlement_vars[field_name] = tk.DoubleVar()
            ttk.Entry(
                entitlements_frame, 
                textvariable=self.entitlement_vars[field_name], 
                width=10
            ).grid(row=row, column=col_start, sticky=tk.W, padx=5, pady=5)
        
        # Deductions
        self.deduction_vars = {}
        deduction_fields = [
            ("التوقفات التقاعدية", "tokifat"),
            ("الضريبة", "dariba"),
            ("العقار", "eikari"),
            ("الرعاية الاجتماعية", "reaya"),
            ("رسم الطابع", "raseem"),
            ("التنفيذ", "tanfeeth"),
            ("الإسكان", "eskan"),
            ("الرشيد", "rasheed"),
            ("الرافدين", "rafidaen"),
            ("الإيجار", "eijar"),
            ("أمانات أخرى", "amanat_okra")
        ]
        
        for i, (label_text, field_name) in enumerate(deduction_fields):
            row = i // 2
            col_start = 0 if i % 2 == 0 else 2
            
            ttk.Label(deductions_frame, text=label_text + ":").grid(
                row=row, column=col_start + 1, sticky=tk.E, padx=5, pady=5
            )
            
            self.deduction_vars[field_name] = tk.DoubleVar()
            ttk.Entry(
                deductions_frame, 
                textvariable=self.deduction_vars[field_name], 
                width=10
            ).grid(row=row, column=col_start, sticky=tk.W, padx=5, pady=5)
        
        # Totals
        self.total_vars = {
            "tin": tk.DoubleVar(),
            "tout": tk.DoubleVar(),
            "alsafi": tk.DoubleVar()
        }
        
        ttk.Label(totals_frame, text="مجموع الاستحقاقات:").grid(row=0, column=1, sticky=tk.E, padx=5, pady=5)
        ttk.Entry(totals_frame, textvariable=self.total_vars["tin"], width=15, state="readonly").grid(
            row=0, column=0, sticky=tk.W, padx=5, pady=5
        )
        
        ttk.Label(totals_frame, text="مجموع الاستقطاعات:").grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)
        ttk.Entry(totals_frame, textvariable=self.total_vars["tout"], width=15, state="readonly").grid(
            row=1, column=0, sticky=tk.W, padx=5, pady=5
        )
        
        ttk.Label(totals_frame, text="الصافي المدفوع:").grid(row=2, column=1, sticky=tk.E, padx=5, pady=5)
        ttk.Entry(
            totals_frame, 
            textvariable=self.total_vars["alsafi"], 
            width=15, 
            state="readonly",
            style="Green.TEntry"
        ).grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Calculate button
        ttk.Button(
            totals_frame, 
            text="حساب المجاميع", 
            command=self.calculate_totals
        ).grid(row=3, column=0, columnspan=2, padx=5, pady=10)
        
    def create_buttons_section(self, parent):
        """Create the buttons section"""
        buttons_frame = ttk.Frame(parent)
        buttons_frame.pack(fill=tk.X, pady=10)
        
        # Save button
        ttk.Button(
            buttons_frame, 
            text="حفظ بيانات الموظف", 
            command=self.save_data
        ).pack(side=tk.RIGHT, padx=5)
        
        # Clear button
        ttk.Button(
            buttons_frame, 
            text="مسح الحقول", 
            command=self.clear_form
        ).pack(side=tk.RIGHT, padx=5)
        
        # Load record button
        ttk.Button(
            buttons_frame, 
            text="تحميل سجل", 
            command=self.load_record
        ).pack(side=tk.LEFT, padx=5)
        
    def load_departments(self):
        """Load departments into the combobox"""
        departments = self.db_manager.get_departments()
        department_names = [dept["name"] for dept in departments]
        self.department_combo["values"] = department_names
        
        if department_names:
            self.department_var.set(department_names[0])
            
    def load_employees(self):
        """Load employees into the combobox"""
        employees = self.db_manager.get_employees()
        employee_names = [f"{emp['name']} ({emp['department_name']})" for emp in employees]
        self.employee_data = {f"{emp['name']} ({emp['department_name']})": emp for emp in employees}
        self.employee_combo["values"] = employee_names
        
    def add_new_department(self):
        """Add a new department"""
        # Simple dialog to get department name
        dialog = tk.Toplevel(self.frame)
        dialog.title("إضافة مجموعة جديدة")
        dialog.geometry("300x100")
        dialog.resizable(False, False)
        
        ttk.Label(dialog, text="اسم المجموعة:").grid(row=0, column=1, padx=5, pady=5)
        dept_name_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=dept_name_var, width=20).grid(row=0, column=0, padx=5, pady=5)
        
        def save_department():
            name = dept_name_var.get().strip()
            if name:
                self.db_manager.add_department(name)
                self.load_departments()
                self.department_var.set(name)
                dialog.destroy()
            else:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المجموعة")
                
        ttk.Button(dialog, text="حفظ", command=save_department).grid(row=1, column=0, columnspan=2, pady=10)
        
        # Make dialog modal
        dialog.transient(self.frame)
        dialog.grab_set()
        self.frame.wait_window(dialog)
        
    def on_employee_selected(self, event):
        """Handle employee selection from combobox"""
        selected = self.employee_selection_var.get()
        if selected in self.employee_data:
            employee = self.employee_data[selected]
            self.current_employee_id = employee["id"]
            
            # Fill employee info
            self.employee_name_var.set(employee["name"])
            self.job_title_var.set(employee["job_title"])
            self.department_var.set(employee["department_name"])
            
            # Try to load the most recent salary record
            self.load_most_recent_record()
            
    def load_most_recent_record(self):
        """Load the most recent salary record for the selected employee"""
        if not self.current_employee_id:
            return
            
        records = self.db_manager.get_employee_salary_records(self.current_employee_id)
        if records:
            # Get the most recent record
            record = records[0]
            
            # Set year and month
            year = record["year"]
            month = record["month"]
            self.year_var.set(str(year))
            
            months = [
                "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            ]
            self.month_var.set(months[month - 1])
            
            # Load the record data
            self.load_record_data(record)
            
    def load_record(self):
        """Load a specific salary record"""
        if not self.current_employee_id:
            messagebox.showerror("خطأ", "يرجى اختيار موظف أولاً")
            return
            
        try:
            year = int(self.year_var.get())
            
            months = [
                "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            ]
            month = months.index(self.month_var.get()) + 1
            
            record = self.db_manager.get_salary_record(self.current_employee_id, year, month)
            
            if record:
                self.load_record_data(record)
                messagebox.showinfo("تحميل السجل", "تم تحميل السجل بنجاح")
            else:
                messagebox.showinfo("تحميل السجل", "لا يوجد سجل للفترة المحددة")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تحميل السجل:\n{str(e)}")
            
    def load_record_data(self, record):
        """Load record data into the form"""
        # Load entitlements
        for field_name in self.entitlement_vars:
            if field_name in record:
                self.entitlement_vars[field_name].set(record[field_name])
                
        # Load deductions
        for field_name in self.deduction_vars:
            if field_name in record:
                self.deduction_vars[field_name].set(record[field_name])
                
        # Load totals
        for field_name in self.total_vars:
            if field_name in record:
                self.total_vars[field_name].set(record[field_name])
                
        # Load note
        if "note" in record:
            self.note_text.delete(1.0, tk.END)
            self.note_text.insert(tk.END, record["note"])
            
    def calculate_totals(self):
        """Calculate totals based on entered values"""
        # Calculate total entitlements
        tin = sum(var.get() for var in self.entitlement_vars.values())
        
        # Calculate total deductions
        tout = sum(var.get() for var in self.deduction_vars.values())
        
        # Calculate net salary
        alsafi = tin - tout
        
        # Update total fields
        self.total_vars["tin"].set(tin)
        self.total_vars["tout"].set(tout)
        self.total_vars["alsafi"].set(alsafi)
        
    def clear_form(self):
        """Clear all form fields"""
        # Clear employee info
        self.employee_name_var.set("")
        self.job_title_var.set("")
        self.employee_selection_var.set("")
        
        # Clear entitlements
        for var in self.entitlement_vars.values():
            var.set(0.0)
            
        # Clear deductions
        for var in self.deduction_vars.values():
            var.set(0.0)
            
        # Clear totals
        for var in self.total_vars.values():
            var.set(0.0)
            
        # Clear note
        self.note_text.delete(1.0, tk.END)
        
        # Reset current employee
        self.current_employee_id = None
        
    def save_data(self):
        """Save employee data to the database"""
        try:
            # Get employee info
            name = self.employee_name_var.get().strip()
            job_title = self.job_title_var.get().strip()
            department_name = self.department_var.get()
            
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الموظف")
                return
                
            if not department_name:
                messagebox.showerror("خطأ", "يرجى اختيار المجموعة")
                return
                
            # Get department ID
            departments = self.db_manager.get_departments()
            department_id = None
            for dept in departments:
                if dept["name"] == department_name:
                    department_id = dept["id"]
                    break
                    
            if not department_id:
                messagebox.showerror("خطأ", "المجموعة غير موجودة")
                return
                
            # Get year and month
            try:
                year = int(self.year_var.get())
                
                months = [
                    "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                    "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
                ]
                month = months.index(self.month_var.get()) + 1
            except:
                messagebox.showerror("خطأ", "يرجى اختيار السنة والشهر بشكل صحيح")
                return
                
            # Calculate totals before saving
            self.calculate_totals()
            
            # Prepare salary data
            salary_data = {}
            
            # Add entitlements
            for field_name, var in self.entitlement_vars.items():
                salary_data[field_name] = var.get()
                
            # Add deductions
            for field_name, var in self.deduction_vars.items():
                salary_data[field_name] = var.get()
                
            # Add note
            salary_data["note"] = self.note_text.get(1.0, tk.END).strip()
            
            # Save or update employee
            if self.current_employee_id:
                employee_id = self.current_employee_id
            else:
                employee_id = self.db_manager.add_employee(name, job_title, department_id)
                self.current_employee_id = employee_id
                
            # Save salary record
            record_id = self.db_manager.add_salary_record(employee_id, year, month, salary_data)
            
            if record_id:
                # Refresh employee list
                self.load_employees()
                
                # Set the employee selection to the current employee
                for key, emp in self.employee_data.items():
                    if emp["id"] == employee_id:
                        self.employee_selection_var.set(key)
                        break
                        
                messagebox.showinfo("حفظ البيانات", "تم حفظ بيانات الموظف بنجاح")
            else:
                messagebox.showerror("خطأ", "حدث خطأ أثناء حفظ بيانات الموظف")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}")