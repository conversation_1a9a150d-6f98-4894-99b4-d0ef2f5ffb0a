@echo off
chcp 65001 > nul
echo تشغيل نظام إدارة رواتب الموظفين بدون مكتبات إضافية...
echo.

REM التحقق من وجود Python
where python >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python غير موجود في النظام أو غير مضاف إلى متغير PATH
    echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
    echo تأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت
    pause
    exit /b
)

REM تعديل ملف main.py مؤقتاً لتجاوز المكتبات غير الضرورية
echo تعديل الكود مؤقتاً لتجاوز المكتبات غير الضرورية...
python -c "
import os

# قراءة ملف main.py
with open('app/main.py', 'r', encoding='utf-8') as f:
    content = f.read()

# تعديل الكود لتجاوز المكتبات غير الضرورية
content = content.replace('import win32gui', '# import win32gui')
content = content.replace('import win32con', '# import win32con')
content = content.replace('hwnd = win32gui.GetForegroundWindow()', '# hwnd = win32gui.GetForegroundWindow()')
content = content.replace('win32gui.ShowWindow(hwnd, win32con.SW_HIDE)', '# win32gui.ShowWindow(hwnd, win32con.SW_HIDE)')

# حفظ الملف المعدل
with open('app/main.py.temp', 'w', encoding='utf-8') as f:
    f.write(content)

# استبدال الملف الأصلي بالملف المعدل
os.replace('app/main.py.temp', 'app/main.py')
"

REM تشغيل البرنامج مع إظهار أي أخطاء
echo تشغيل البرنامج...
python app/main.py
if %ERRORLEVEL% NEQ 0 (
    echo حدث خطأ أثناء تشغيل البرنامج
    pause
)