# دليل المستخدم - نظام إدارة رواتب الموظفين

## مقدمة

نظام إدارة رواتب الموظفين هو برنامج سطح مكتب مصمم لتسهيل إدارة رواتب الموظفين في المؤسسات. يوفر البرنامج واجهة سهلة الاستخدام تدعم اللغة العربية بالكامل.

## بدء الاستخدام

1. قم بتثبيت المكتبات المطلوبة باستخدام ملف `install_dependencies.bat`
2. قم بتشغيل البرنامج باستخدام ملف `run_app.bat`

## الواجهة الرئيسية

تتكون الواجهة الرئيسية من خمسة تبويبات:

### 1. إدخال بيانات الموظف

في هذا التبويب يمكنك:
- إضافة موظف جديد
- تعديل بيانات موظف موجود
- إدخال تفاصيل الراتب الشهري
- حساب المجاميع تلقائياً

#### خطوات إضافة موظف جديد:
1. أدخل اسم الموظف
2. أدخل العنوان الوظيفي
3. اختر المجموعة (أو أضف مجموعة جديدة)
4. أدخل تفاصيل الراتب
5. اضغط على زر "حساب المجاميع" للتحقق من الحسابات
6. اضغط على زر "حفظ بيانات الموظف"

### 2. جلب البيانات من ملف Excel

يمكنك استيراد بيانات الموظفين والرواتب من ملف Excel:
1. اضغط على زر "استعراض" لاختيار ملف Excel
2. اختر المجموعة التي ستنتمي إليها البيانات المستوردة
3. حدد السنة والشهر
4. اضغط على زر "تحميل البيانات" لمعاينة البيانات
5. اضغط على زر "استيراد البيانات" لحفظها في قاعدة البيانات

#### تنسيق ملف Excel:
يجب أن يحتوي ملف Excel على الأعمدة التالية على الأقل:
- EmployName: اسم الموظف
- JobTitle: العنوان الوظيفي

### 3. سجل الموظف الشهري

يعرض هذا التبويب سجل الرواتب الشهرية للموظف:
1. اختر الموظف من القائمة
2. اختر السنة
3. ستظهر جميع سجلات الراتب الشهرية للموظف في السنة المحددة
4. يعرض رسماً بيانياً يوضح تطور الراتب خلال السنة

### 4. طباعة

يمكنك طباعة وتصدير التقارير التالية:
- سجل موظف شهري
- سجل موظف سنوي
- قائمة رواتب شهرية للمجموعة
- تقرير إحصائي

خطوات إنشاء تقرير:
1. اختر نوع التقرير
2. حدد المعلومات المطلوبة (الموظف، المجموعة، السنة، الشهر)
3. اضغط على زر "معاينة" لمعاينة التقرير
4. اضغط على زر "طباعة" أو "تصدير إلى PDF" أو "تصدير إلى Excel"

### 5. تصفية حسب

يمكنك البحث وتصفية سجلات الرواتب حسب معايير مختلفة:
1. حدد معايير التصفية (الموظف، المجموعة، العنوان الوظيفي، السنة، الشهر، نطاق الراتب)
2. اضغط على زر "بحث"
3. ستظهر النتائج في الجدول أدناه
4. يمكنك النقر المزدوج على أي سجل لعرض تفاصيله
5. يعرض رسماً بيانياً يلخص نتائج البحث

## النسخ الاحتياطي واستعادة البيانات

لعمل نسخة احتياطية من البيانات:
1. اضغط على زر "نسخ احتياطي" في أسفل الواجهة الرئيسية
2. اختر مكان حفظ ملف النسخة الاحتياطية (بصيغة JSON)

لاستعادة البيانات من نسخة احتياطية:
1. استخدم وظيفة الاستيراد من ملف JSON (متوفرة في قائمة الملف)

## نصائح وإرشادات

- قم بعمل نسخة احتياطية بشكل دوري للحفاظ على البيانات
- تأكد من إدخال البيانات بشكل صحيح قبل الحفظ
- استخدم وظيفة "حساب المجاميع" للتحقق من صحة الحسابات
- يمكنك استخدام وظيفة التصفية للعثور بسرعة على سجلات محددة

## استكشاف الأخطاء وإصلاحها

إذا واجهت أي مشاكل في البرنامج:
1. تأكد من تثبيت جميع المكتبات المطلوبة
2. تحقق من وجود قاعدة البيانات في المسار الصحيح
3. تأكد من صلاحيات الوصول إلى المجلدات
4. راجع سجل الأخطاء للحصول على مزيد من المعلومات