On Error Resume Next

Set WshShell = CreateObject("WScript.Shell")
CurrentDirectory = CreateObject("Scripting.FileSystemObject").GetParentFolderName(WScript.ScriptFullName)
WshShell.CurrentDirectory = CurrentDirectory

' التحقق من وجود Python
Set objExec = WshShell.Exec("cmd /c where python")
If Err.Number <> 0 Or objExec.StdOut.ReadAll = "" Then
    MsgBox "Python غير موجود في النظام أو غير مضاف إلى متغير PATH" & vbCrLf & _
           "يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/" & vbCrLf & _
           "تأكد من تفعيل خيار ""Add Python to PATH"" أثناء التثبيت", vbExclamation, "خطأ"
    WScript.Quit
End If

' التحقق من وجود الملفات الضرورية
Set fso = CreateObject("Scripting.FileSystemObject")
If Not fso.FileExists(CurrentDirectory & "\app\main.py") Then
    MsgBox "ملف main.py غير موجود!", vbExclamation, "خطأ"
    WScript.Quit
End If

' تشغيل البرنامج
WshShell.Run "cmd /c python app/main.py", 0, False