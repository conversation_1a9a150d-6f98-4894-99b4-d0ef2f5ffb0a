#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Main Window for Employee Salary Management System
Handles the main application window and tabs
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

# Add the app directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.ui.employee_tab import EmployeeTab
from app.ui.excel_import_tab import ExcelImportTab
from app.ui.employee_record_tab import EmployeeRecordTab
from app.ui.print_tab import PrintTab
from app.ui.filter_tab import FilterTab
from app.ui.about_tab import AboutTab
from app.ui.intro_screen import show_intro

class MainWindow:
    """Main application window with tabs"""
    
    def __init__(self, root, db_manager):
        """Initialize the main window"""
        self.root = root
        self.db_manager = db_manager
        
        # Configure root window for RTL (Right-to-Left) layout
        self.root.tk.call('encoding', 'system', 'utf-8')
        
        # Try to load and apply RTL support
        try:
            self.root.tk.call('package', 'require', 'Ttk')
            self.root.tk.call('namespace', 'import', '::ttk::*')
            self.root.tk.call('source', 'theme_rtl.tcl')
        except tk.TclError:
            print("Could not load RTL theme, using default")
        
        # Set background color for the root window - light beige, light blue and light red mix
        root.configure(background='#FFF5F5')  # Very light mixed background
        
        # Create main frame with background color
        self.main_frame = ttk.Frame(root, style='TFrame')
        self.main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook (tabbed interface)
        self.notebook = ttk.Notebook(self.main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Configure style for frames to ensure consistent background with soft gradient colors
        style = ttk.Style()
        style.configure('TFrame', background='#FFF5F5')  # Very light mixed background
        style.configure('TNotebook', background='#FFF5F5')
        style.configure('TNotebook.Tab', background='#FFE6E6', padding=[10, 2])  # Light red tabs
        
        # Configure additional styles for various widgets to match the color scheme
        style.configure('TLabel', background='#FFF5F5', foreground='#4B0082')  # Indigo text
        style.configure('TButton', background='#6495ED', foreground='#FFFFFF')  # Cornflower blue buttons with white text
        style.configure('TEntry', fieldbackground='#FFFAF0', foreground='#4B0082')  # Light beige entry fields with indigo text
        style.configure('TCombobox', fieldbackground='#FFFAF0', foreground='#4B0082')
        style.configure('Treeview', background='#FFFAF0', fieldbackground='#FFFAF0', foreground='#4B0082')
        style.configure('TLabelframe', background='#FFF5F5')
        style.configure('TLabelframe.Label', background='#FFF5F5', foreground='#8A2BE2')  # BlueViolet text for labels
        
        # Create custom button styles with different colors
        style.configure('Blue.TButton', background='#6495ED', foreground='#FFFFFF')  # Cornflower blue
        style.configure('Purple.TButton', background='#8A2BE2', foreground='#FFFFFF')  # BlueViolet
        style.configure('Pink.TButton', background='#DB7093', foreground='#FFFFFF')  # PaleVioletRed (pink)
        
        # Create tabs
        self.create_tabs()
        
        # Create status bar
        self.create_status_bar()
        
        # Create menu
        self.create_menu()
        
        # Show intro screen
        show_intro(self.root, self.after_intro)
        
    def after_intro(self):
        """Called after intro screen closes"""
        # Nothing special to do here, just continue with the main window
        pass
        
    def create_tabs(self):
        """Create the application tabs"""
        # Employee tab
        self.employee_tab = EmployeeTab(self.notebook, self.db_manager)
        self.notebook.add(self.employee_tab.frame, text="إدخال بيانات الموظف")
        
        # Excel import tab
        self.excel_import_tab = ExcelImportTab(self.notebook, self.db_manager)
        self.notebook.add(self.excel_import_tab.frame, text="جلب البيانات من ملف Excel")
        
        # Employee record tab
        self.employee_record_tab = EmployeeRecordTab(self.notebook, self.db_manager)
        self.notebook.add(self.employee_record_tab.frame, text="سجل الموظف الشهري")
        
        # Print tab
        self.print_tab = PrintTab(self.notebook, self.db_manager)
        self.notebook.add(self.print_tab.frame, text="طباعة")
        
        # Filter tab
        self.filter_tab = FilterTab(self.notebook, self.db_manager)
        self.notebook.add(self.filter_tab.frame, text="تصفية حسب")
        
        # About tab
        self.about_tab = AboutTab(self.notebook, self.db_manager)
        self.notebook.add(self.about_tab.frame, text="حول البرنامج")
        
        # Add developer credit to each tab
        self.add_developer_credit(self.employee_tab.frame)
        self.add_developer_credit(self.excel_import_tab.frame)
        self.add_developer_credit(self.employee_record_tab.frame)
        self.add_developer_credit(self.print_tab.frame)
        self.add_developer_credit(self.filter_tab.frame)
        # No need to add developer credit to the about tab as it already contains developer info
        
    def add_developer_credit(self, parent):
        """Add developer credit to the bottom of each tab"""
        # Create a frame for the developer credit
        credit_frame = ttk.Frame(parent)
        credit_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=2)
        
        # Create a label with the developer credit
        credit_label = ttk.Label(
            credit_frame, 
            text="تطوير: المحاسب المبرمج علي عاجل خشان المحنة",
            foreground="#8A2BE2",  # Purple color
            font=("Arial", 8)
        )
        credit_label.pack(side=tk.RIGHT, padx=5)
        
        # Add animation effect
        self.animate_credit_label(credit_label)
        
    def animate_credit_label(self, label):
        """Add color animation to the developer credit label"""
        import colorsys
        
        def update_color(hue=0):
            # Convert HSV to RGB (hue, 0.7, 1.0)
            r, g, b = colorsys.hsv_to_rgb(hue, 0.7, 1.0)
            
            # Convert to hex color
            color = f"#{int(r*255):02x}{int(g*255):02x}{int(b*255):02x}"
            
            # Update label color
            label.configure(foreground=color)
            
            # Increment hue (cycle through colors)
            hue = (hue + 0.01) % 1.0
            
            # Schedule next update
            self.root.after(100, update_color, hue)
            
        # Start animation
        update_color()
        
    def create_status_bar(self):
        """Create the status bar"""
        self.status_bar = ttk.Frame(self.main_frame, relief=tk.SUNKEN)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Status message
        self.status_var = tk.StringVar()
        self.status_var.set("جاهز")
        status_label = ttk.Label(self.status_bar, textvariable=self.status_var, anchor=tk.W)
        status_label.pack(side=tk.RIGHT, padx=5, pady=2)
        
        # Database info
        db_label = ttk.Label(self.status_bar, text=f"قاعدة البيانات: {self.db_manager.db_path}", anchor=tk.W)
        db_label.pack(side=tk.LEFT, padx=5, pady=2)
        
    def create_menu(self):
        """Create the application menu"""
        self.menu_bar = tk.Menu(self.root)
        self.root.config(menu=self.menu_bar)
        
        # File menu
        file_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="نسخ احتياطي", command=self.backup_database)
        file_menu.add_command(label="استعادة من نسخة احتياطية", command=self.restore_database)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.exit_application)
        
        # Help menu
        help_menu = tk.Menu(self.menu_bar, tearoff=0)
        self.menu_bar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="عن البرنامج", command=self.show_about)
        help_menu.add_command(label="تعليمات", command=self.show_help)
        
    def backup_database(self):
        """Backup the database"""
        try:
            from tkinter import filedialog
            import json
            import datetime
            
            # Ask for save location
            file_path = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json")],
                title="حفظ النسخة الاحتياطية"
            )
            
            if not file_path:
                return  # User cancelled
                
            # Get all data
            departments = self.db_manager.get_departments()
            employees = self.db_manager.get_employees()
            
            # Get all salary records
            self.db_manager.cursor.execute("SELECT * FROM salary_records")
            salary_records = [dict(row) for row in self.db_manager.cursor.fetchall()]
            
            # Create backup data
            backup_data = {
                "timestamp": datetime.datetime.now().isoformat(),
                "departments": departments,
                "employees": employees,
                "salary_records": salary_records
            }
            
            # Save to file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=4)
                
            messagebox.showinfo(
                "نسخ احتياطي", 
                f"تم حفظ النسخة الاحتياطية بنجاح إلى:\n{file_path}"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء عمل النسخة الاحتياطية:\n{str(e)}")
            
    def restore_database(self):
        """Restore the database from backup"""
        try:
            from tkinter import filedialog
            import json
            
            # Ask for backup file
            file_path = filedialog.askopenfilename(
                filetypes=[("JSON files", "*.json")],
                title="اختر ملف النسخة الاحتياطية"
            )
            
            if not file_path:
                return  # User cancelled
                
            # Load backup data
            with open(file_path, 'r', encoding='utf-8') as f:
                backup_data = json.load(f)
                
            # Confirm restore
            confirm = messagebox.askyesno(
                "استعادة البيانات", 
                "سيتم استبدال جميع البيانات الحالية بالبيانات من النسخة الاحتياطية.\n\n"
                "هل أنت متأكد من الاستمرار؟"
            )
            
            if not confirm:
                return
                
            # Clear current data
            self.db_manager.cursor.execute("DELETE FROM salary_records")
            self.db_manager.cursor.execute("DELETE FROM employees")
            self.db_manager.cursor.execute("DELETE FROM departments")
            self.db_manager.conn.commit()
            
            # Restore departments
            for dept in backup_data["departments"]:
                self.db_manager.cursor.execute(
                    "INSERT INTO departments (id, name) VALUES (?, ?)",
                    (dept["id"], dept["name"])
                )
                
            # Restore employees
            for emp in backup_data["employees"]:
                self.db_manager.cursor.execute(
                    "INSERT INTO employees (id, name, job_title, department_id) VALUES (?, ?, ?, ?)",
                    (emp["id"], emp["name"], emp["job_title"], emp["department_id"])
                )
                
            # Restore salary records
            for record in backup_data["salary_records"]:
                # Get all fields except id
                fields = [k for k in record.keys() if k != "id"]
                placeholders = ", ".join(["?"] * len(fields))
                values = [record[field] for field in fields]
                
                query = f"INSERT INTO salary_records ({', '.join(fields)}) VALUES ({placeholders})"
                self.db_manager.cursor.execute(query, values)
                
            # Commit changes
            self.db_manager.conn.commit()
            
            # Refresh UI
            self.employee_tab.load_departments()
            self.employee_tab.load_employees()
            self.excel_import_tab.load_departments()
            self.employee_record_tab.load_departments()
            self.employee_record_tab.load_employees()
            self.print_tab.load_departments()
            self.print_tab.load_employees()
            self.filter_tab.load_departments()
            self.filter_tab.load_employees()
            
            messagebox.showinfo(
                "استعادة البيانات", 
                "تم استعادة البيانات بنجاح من النسخة الاحتياطية"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء استعادة البيانات:\n{str(e)}")
            
    def exit_application(self):
        """Exit the application"""
        confirm = messagebox.askyesno("خروج", "هل أنت متأكد من الخروج من البرنامج؟")
        if confirm:
            self.root.destroy()
            
    def show_about(self):
        """Show about dialog"""
        about_window = tk.Toplevel(self.root)
        about_window.title("عن البرنامج")
        about_window.geometry("400x300")
        about_window.resizable(False, False)
        
        # Make window modal
        about_window.transient(self.root)
        about_window.grab_set()
        
        # Center window
        about_window.update_idletasks()
        width = about_window.winfo_width()
        height = about_window.winfo_height()
        x = (about_window.winfo_screenwidth() // 2) - (width // 2)
        y = (about_window.winfo_screenheight() // 2) - (height // 2)
        about_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        # Create content
        frame = ttk.Frame(about_window, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            frame, 
            text="نظام إدارة رواتب الموظفين", 
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=10)
        
        # Version
        version_label = ttk.Label(frame, text="الإصدار: v2.0", font=("Arial", 12))
        version_label.pack(pady=5)
        
        # Developer
        developer_label = ttk.Label(
            frame, 
            text="تطوير: المحاسب المبرمج علي عاجل خشان المحنة", 
            font=("Arial", 12)
        )
        developer_label.pack(pady=5)
        
        # Copyright
        copyright_label = ttk.Label(
            frame, 
            text="جميع الحقوق محفوظة © 2023", 
            font=("Arial", 10)
        )
        copyright_label.pack(pady=5)
        
        # Description
        description_text = (
            "برنامج سطح مكتب احترافي لإدارة رواتب الموظفين "
            "مع واجهة رسومية حديثة تدعم التفاعلية والرسوميات الإحصائية."
        )
        description_label = ttk.Label(
            frame, 
            text=description_text, 
            wraplength=350, 
            justify=tk.CENTER
        )
        description_label.pack(pady=10)
        
        # Close button
        close_button = ttk.Button(
            frame, 
            text="إغلاق", 
            command=about_window.destroy,
            style="Red.TButton"
        )
        close_button.pack(pady=10)
        
    def show_help(self):
        """Show help dialog"""
        try:
            import os
            import webbrowser
            
            # Check if HELP.md exists
            help_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "HELP.md")
            
            if os.path.exists(help_path):
                # Open the help file in the default application
                webbrowser.open(help_path)
            else:
                # Show simple help dialog
                self.show_simple_help()
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء فتح ملف المساعدة:\n{str(e)}")
            self.show_simple_help()
            
    def show_simple_help(self):
        """Show a simple help dialog"""
        help_window = tk.Toplevel(self.root)
        help_window.title("تعليمات")
        help_window.geometry("500x400")
        help_window.resizable(True, True)
        
        # Make window modal
        help_window.transient(self.root)
        help_window.grab_set()
        
        # Center window
        help_window.update_idletasks()
        width = help_window.winfo_width()
        height = help_window.winfo_height()
        x = (help_window.winfo_screenwidth() // 2) - (width // 2)
        y = (help_window.winfo_screenheight() // 2) - (height // 2)
        help_window.geometry('{}x{}+{}+{}'.format(width, height, x, y))
        
        # Create content
        frame = ttk.Frame(help_window, padding=20)
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            frame, 
            text="تعليمات استخدام البرنامج", 
            font=("Arial", 16, "bold")
        )
        title_label.pack(pady=10)
        
        # Create scrollable text area
        text_frame = ttk.Frame(frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(text_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        help_text = tk.Text(
            text_frame, 
            wrap=tk.WORD, 
            yscrollcommand=scrollbar.set,
            font=("Arial", 10),
            padx=10,
            pady=10
        )
        help_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        scrollbar.config(command=help_text.yview)
        
        # Help content
        help_content = """
# دليل المستخدم - نظام إدارة رواتب الموظفين

## التبويبات الرئيسية

### 1. إدخال بيانات الموظف
- إضافة موظف جديد
- تعديل بيانات موظف موجود
- إدخال تفاصيل الراتب الشهري

### 2. جلب البيانات من ملف Excel
- استيراد بيانات الموظفين والرواتب من ملف Excel
- اختيار المجموعة والسنة والشهر

### 3. سجل الموظف الشهري
- عرض سجلات الرواتب الشهرية للموظف
- تصفية حسب المجموعة والسنة

### 4. طباعة
- طباعة وتصدير التقارير بصيغة PDF و Excel
- أنواع التقارير: سجل موظف شهري، سجل موظف سنوي، قائمة رواتب شهرية للمجموعة، تقرير إحصائي

### 5. تصفية حسب
- البحث وتصفية سجلات الرواتب حسب معايير مختلفة
- عرض نتائج البحث في جدول

## النسخ الاحتياطي واستعادة البيانات
- استخدم قائمة "ملف" > "نسخ احتياطي" لعمل نسخة احتياطية من البيانات
- استخدم قائمة "ملف" > "استعادة من نسخة احتياطية" لاستعادة البيانات

## للمزيد من المساعدة
راجع ملف HELP.md للحصول على تعليمات مفصلة حول استخدام البرنامج.
        """
        
        help_text.insert(tk.END, help_content)
        help_text.config(state=tk.DISABLED)
        
        # Close button
        close_button = ttk.Button(
            frame, 
            text="إغلاق", 
            command=help_window.destroy,
            style="Red.TButton"
        )
        close_button.pack(pady=10)