@echo off
chcp 65001 > nul
echo تشغيل نظام إدارة رواتب الموظفين بدون التحقق من المتطلبات...
echo.

REM التحقق من وجود Python فقط
where python >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo Python غير موجود في النظام أو غير مضاف إلى متغير PATH
    echo يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
    echo تأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت
    pause
    exit /b
)

REM تشغيل البرنامج مباشرة مع إظهار أي أخطاء
echo تشغيل البرنامج...
python app/main.py
if %ERRORLEVEL% NEQ 0 (
    echo حدث خطأ أثناء تشغيل البرنامج
    pause
)