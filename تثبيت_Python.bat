@echo off
echo ===== تثبيت Python لنظام إدارة رواتب الموظفين =====
echo.
echo سيتم تنزيل وتثبيت Python 3.9.7 (64-bit)
echo يرجى الانتظار...
echo.

REM تنزيل Python
echo تنزيل Python...
powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.9.7/python-3.9.7-amd64.exe' -OutFile 'python-installer.exe'}"

if not exist python-installer.exe (
    echo فشل تنزيل Python. يرجى تنزيله يدوياً من الموقع الرسمي:
    echo https://www.python.org/downloads/
    pause
    exit /b
)

REM تثبيت Python
echo تثبيت Python...
echo يرجى تفعيل خيار "Add Python 3.9 to PATH" في نافذة التثبيت
python-installer.exe /passive PrependPath=1

echo.
echo بعد اكتمال التثبيت، يرجى إعادة تشغيل الكمبيوتر ثم تشغيل البرنامج باستخدام ملف run_app.bat
pause