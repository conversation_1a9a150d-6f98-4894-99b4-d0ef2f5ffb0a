# تعليمات التثبيت - نظام إدارة رواتب الموظفين

## متطلبات النظام

- نظام تشغيل: Windows 7/8/10/11
- Python 3.8 أو أحدث
- مساحة تخزين: 50 ميجابايت على الأقل
- ذاكرة: 2 جيجابايت على الأقل

## خطوات التثبيت

1. قم بتثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
   - تأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت

2. قم بفك ضغط ملف `نظام_إدارة_رواتب_الموظفين_v1.0.zip` إلى المجلد الذي تريده

3. افتح مجلد البرنامج وقم بتشغيل ملف `install_dependencies.bat` بالنقر المزدوج عليه
   - هذا سيقوم بتثبيت جميع المكتبات المطلوبة للبرنامج

4. بعد اكتمال تثبيت المكتبات، يمكنك تشغيل البرنامج بالنقر المزدوج على ملف `run_app.bat`

## حل المشكلات الشائعة

### مشكلة: خطأ عند تشغيل ملف install_dependencies.bat

**الحل:**
- تأكد من تثبيت Python بشكل صحيح
- تأكد من إضافة Python إلى متغير PATH
- جرب تشغيل الملف كمسؤول (Run as Administrator)

### مشكلة: خطأ عند تشغيل البرنامج

**الحل:**
- تأكد من تثبيت جميع المكتبات المطلوبة
- يمكنك تثبيت المكتبات يدوياً باستخدام الأمر التالي في موجه الأوامر:
  ```
  pip install -r requirements.txt
  ```

### مشكلة: البرنامج لا يعرض النصوص العربية بشكل صحيح

**الحل:**
- تأكد من أن نظام التشغيل يدعم اللغة العربية
- تأكد من تثبيت الخطوط العربية على النظام

## الترقية من إصدار سابق

إذا كنت تستخدم إصداراً سابقاً من البرنامج:

1. قم بعمل نسخة احتياطية من قاعدة البيانات الحالية
2. قم بتثبيت الإصدار الجديد في مجلد منفصل
3. انسخ ملف قاعدة البيانات من المجلد القديم إلى المجلد الجديد

## للمزيد من المساعدة

راجع ملف `HELP.md` للحصول على تعليمات مفصلة حول استخدام البرنامج.