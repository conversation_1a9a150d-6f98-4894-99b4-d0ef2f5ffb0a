# نظام إدارة رواتب الموظفين

## نظرة عامة
نظام متكامل لإدارة رواتب الموظفين باللغة العربية، يتيح إدخال بيانات الموظفين وإدارة رواتبهم الشهرية وطباعة التقارير.

## متطلبات النظام
- نظام تشغيل Windows 7/8/10/11
- Python 3.7 أو أحدث (مطلوب)

## التثبيت لأول مرة

### الخطوة 1: تثبيت Python
قبل تشغيل البرنامج، يجب تثبيت Python:

1. قم بتشغيل ملف `تثبيت_Python.bat` لتنزيل وتثبيت Python تلقائياً
   - أو قم بتثبيت Python يدوياً من الموقع الرسمي: https://www.python.org/downloads/
   - **مهم جداً**: تأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت

2. بعد تثبيت Python، قم بإعادة تشغيل الكمبيوتر

### الخطوة 2: تثبيت المكتبات المطلوبة
قم بتشغيل ملف `install_dependencies.bat` لتثبيت المكتبات المطلوبة

## طرق تشغيل البرنامج

### الطريقة الأولى (موصى بها)
انقر نقراً مزدوجاً على ملف `run_app.bat` لتشغيل البرنامج.

### الطريقة الثانية
انقر نقراً مزدوجاً على ملف `تشغيل_البرنامج.vbs` لتشغيل البرنامج بدون ظهور نافذة cmd.

### الطريقة الثالثة
قم بتشغيل ملف `إنشاء_اختصار.bat` لإنشاء اختصار على سطح المكتب، ثم استخدم هذا الاختصار لتشغيل البرنامج.

## حل المشكلات الشائعة

### البرنامج لا يعمل
إذا كان البرنامج لا يعمل، جرب الخطوات التالية:

1. تأكد من تثبيت Python بشكل صحيح:
   - قم بتشغيل ملف `تشخيص.bat` للتحقق من وجود Python
   - إذا لم يتم العثور على Python، قم بتشغيل ملف `تثبيت_Python.bat`

2. تأكد من تثبيت المكتبات المطلوبة:
   - قم بتشغيل ملف `install_dependencies.bat` مرة أخرى

3. جرب تشغيل البرنامج مباشرة من سطر الأوامر:
   - افتح موجه الأوامر (cmd)
   - انتقل إلى مجلد البرنامج: `cd "مسار_المجلد\الرواتب 2"`
   - قم بتشغيل البرنامج: `python app/main.py`

## الميزات الرئيسية

- واجهة مستخدم رسومية تدعم اللغة العربية بالكامل
- إدخال بيانات الموظفين وإدارة المجموعات/الأقسام
- استيراد وتصدير البيانات من/إلى Excel
- إدارة سجلات الرواتب الشهرية
- طباعة وتصدير التقارير بصيغة PDF
- تصفية وبحث في البيانات
- نسخ احتياطي واستعادة البيانات

## المطور
المحاسب المبرمج: علي عاجل خشان المحنة

## الإصدار
v2.0