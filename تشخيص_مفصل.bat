@echo off
chcp 65001 > nul
echo ===== تشخيص مفصل لنظام إدارة رواتب الموظفين =====
echo.
echo [1/5] التحقق من وجود Python...
where python >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo [X] Python غير موجود في النظام أو غير مضاف إلى متغير PATH
    echo     يرجى تثبيت Python من الموقع الرسمي: https://www.python.org/downloads/
    echo     تأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت
) else (
    echo [✓] تم العثور على Python
    python --version
)

echo.
echo [2/5] التحقق من وجود الملفات الضرورية...
if exist app\main.py (
    echo [✓] تم العثور على ملف main.py
) else (
    echo [X] ملف main.py غير موجود!
)

echo.
echo [3/5] التحقق من المكتبات المطلوبة...
python -c "import sys; print('Python Path:', sys.executable)"
echo.
echo المكتبات الأساسية:
python -c "import sys; print('[✓]' if 'tkinter' in sys.modules or __import__('tkinter') else '[X]', 'tkinter')" 2>nul || echo [X] tkinter
python -c "import sys; print('[✓]' if 'pandas' in sys.modules or __import__('pandas') else '[X]', 'pandas')" 2>nul || echo [X] pandas
python -c "import sys; print('[✓]' if 'matplotlib' in sys.modules or __import__('matplotlib') else '[X]', 'matplotlib')" 2>nul || echo [X] matplotlib
python -c "import sys; print('[✓]' if 'openpyxl' in sys.modules or __import__('openpyxl') else '[X]', 'openpyxl')" 2>nul || echo [X] openpyxl
python -c "import sys; print('[✓]' if 'win32gui' in sys.modules or __import__('win32gui') else '[X]', 'win32gui (pywin32)')" 2>nul || echo [X] win32gui (pywin32)

echo.
echo [4/5] تثبيت المكتبات المفقودة...
echo تثبيت pywin32 (مطلوب لإخفاء نافذة CMD)...
pip install pywin32
echo تثبيت باقي المكتبات من ملف requirements.txt...
pip install -r requirements.txt

echo.
echo [5/5] محاولة تشغيل البرنامج مع تفعيل رسائل الخطأ...
python -c "
import os, sys
sys.path.append(os.path.dirname(os.getcwd()))
try:
    from app.main import Application
    print('[✓] تم استيراد التطبيق بنجاح')
    try:
        app = Application()
        print('[✓] تم إنشاء كائن التطبيق بنجاح')
        print('[i] جاري تشغيل التطبيق...')
        app.run()
    except Exception as e:
        print('[X] حدث خطأ أثناء تشغيل التطبيق:', e)
        import traceback
        traceback.print_exc()
except Exception as e:
    print('[X] حدث خطأ أثناء استيراد التطبيق:', e)
    import traceback
    traceback.print_exc()
"

echo.
echo انتهى التشخيص
pause