#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Employee Salary Management System
Main Application Entry Point
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox

# Hide console window - make this optional to avoid crashes
try:
    import win32gui
    import win32con
    hwnd = win32gui.GetForegroundWindow()
    win32gui.ShowWindow(hwnd, win32con.SW_HIDE)
except Exception as e:
    print(f"تحذير: لا يمكن إخفاء نافذة CMD: {e}")
    # Continue without hiding the console window

# Add the app directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.ui.main_window import MainWindow
from app.database.db_manager import DatabaseManager

class Application:
    """Main application class that initializes the system"""
    
    def __init__(self):
        """Initialize the application"""
        self.root = tk.Tk()
        self.root.title("نظام إدارة رواتب الموظفين")
        self.root.geometry("1200x700")
        self.root.minsize(1000, 600)
        
        # Set up Arabic support
        self.root.option_add('*Font', 'Arial 10')
        
        # Try to set application icon
        try:
            icon_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "resources", "app_icon.ico")
            if os.path.exists(icon_path):
                self.root.iconbitmap(icon_path)
        except Exception as e:
            print(f"Could not set application icon: {e}")
        
        # Initialize database
        self.db_manager = DatabaseManager()
        
        # Configure style
        self.setup_styles()
        
        # Create main window
        self.main_window = MainWindow(self.root, self.db_manager)
        
    def setup_styles(self):
        """Set up the application styles"""
        style = ttk.Style()
        style.theme_use('clam')  # Use a theme that works well with Arabic
        
        # Define custom colors
        bg_color = '#FFECF5'  # Light pink (بنبي زهري فاتح)
        bg_color_alt = '#F8E6FF'  # Very light purple (بنفسجي فاتح جدا)
        button_color = '#0080FF'  # Bright blue (أزرق براق)
        exit_button_color = '#FF0000'  # Bright red (أحمر ناصع)
        
        # Configure colors for all widgets
        style.configure('TFrame', background=bg_color)
        style.configure('TLabelframe', background=bg_color)
        style.configure('TLabelframe.Label', background=bg_color, font=('Arial', 10, 'bold'))
        
        # Configure notebook (tabs)
        style.configure('TNotebook', background=bg_color_alt)
        style.configure('TNotebook.Tab', padding=[10, 5], font=('Arial', 12, 'bold'))
        style.map('TNotebook.Tab', background=[('selected', button_color)], foreground=[('selected', 'white')])
        
        # Configure buttons with larger font
        style.configure('TButton', font=('Arial', 12, 'bold'), background=button_color, foreground='white')
        style.map('TButton', background=[('active', '#0066CC')])
        
        # Configure exit button
        style.configure('Red.TButton', font=('Arial', 12, 'bold'), background=exit_button_color, foreground='white')
        style.map('Red.TButton', background=[('active', '#CC0000')])
        
        # Configure entry fields
        style.configure('TEntry', fieldbackground='white', font=('Arial', 11))
        
        # Configure special entry styles
        style.configure('Green.TEntry', fieldbackground='#E6FFE6')  # Light green
        style.configure('Orange.TEntry', fieldbackground='#FFE6CC')  # Light orange
        
        # Configure labels
        style.configure('TLabel', background=bg_color, font=('Arial', 11))
        
        # Configure combobox
        style.configure('TCombobox', fieldbackground='white', font=('Arial', 11))
        style.configure('Green.TCombobox', fieldbackground='#E6FFE6', font=('Arial', 11))
        
        # Configure treeview (tables)
        style.configure('Treeview', background='white', font=('Arial', 10))
        style.configure('Treeview.Heading', font=('Arial', 11, 'bold'))
        
    def run(self):
        """Run the application"""
        self.root.mainloop()

if __name__ == "__main__":
    app = Application()
    app.run()