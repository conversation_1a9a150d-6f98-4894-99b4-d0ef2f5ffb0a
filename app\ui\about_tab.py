#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
About Tab for Employee Salary Management System
Contains information about the program, developer, and data clearing functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys
from datetime import datetime

# Add the app directory to the path so we can import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class AboutTab:
    """Tab for displaying information about the program and developer"""
    
    def __init__(self, parent, db_manager):
        """Initialize the about tab"""
        self.parent = parent
        self.db_manager = db_manager
        
        # Create main frame with gradient background
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # Set the background color
        self.set_background_color()
        
        # Create the content
        self.create_about_content()
        self.create_data_clearing_section()
        
    def set_background_color(self):
        """Set a light gradient background for the tab with scrolling capability"""
        # Create a main frame to hold the canvas and scrollbar
        self.main_container = ttk.Frame(self.frame)
        self.main_container.pack(fill=tk.BOTH, expand=True)
        
        # Create a canvas for the gradient background with scrolling
        self.canvas = tk.Canvas(self.main_container, highlightthickness=0)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # Add vertical scrollbar
        self.scrollbar = ttk.Scrollbar(self.main_container, orient=tk.VERTICAL, command=self.canvas.yview)
        self.scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Configure canvas to use scrollbar
        self.canvas.configure(yscrollcommand=self.scrollbar.set)
        
        # Bind resize event to redraw the gradient
        self.canvas.bind("<Configure>", self.on_canvas_configure)
        
        # Create a frame on top of the canvas for content
        self.content_frame = ttk.Frame(self.canvas)
        self.canvas_window = self.canvas.create_window(0, 0, anchor="nw", window=self.content_frame)
        
        # Update scrollregion when content frame size changes
        self.content_frame.bind("<Configure>", self.on_frame_configure)
        
        # Bind mouse wheel to scroll
        self.canvas.bind_all("<MouseWheel>", self.on_mousewheel)
        
    def on_canvas_configure(self, event=None):
        """Handle canvas resize event"""
        # Redraw gradient and update scroll region
        self.draw_gradient(event)
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        
        # Make sure the content frame width matches the canvas width
        if event:
            self.canvas.itemconfig(self.canvas_window, width=event.width)
    
    def on_frame_configure(self, event=None):
        """Update the scroll region when the content frame size changes"""
        # Update the scrollregion to encompass the inner frame
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    
    def on_mousewheel(self, event):
        """Handle mouse wheel scrolling"""
        # Scroll up/down (-/+)
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    def draw_gradient(self, event=None):
        """Draw a soft gradient background"""
        width = self.canvas.winfo_width()
        height = max(self.canvas.winfo_height(), self.content_frame.winfo_reqheight())
        
        # Clear previous gradient
        self.canvas.delete("gradient")
        
        # Create a soft gradient with light beige, light blue and light red
        for i in range(height):
            # Calculate color components based on position
            r = int(255 - (i / height * 20))  # Light red component
            g = int(245 - (i / height * 15))  # Light beige component
            b = int(240 + (i / height * 15))  # Light blue component
            
            # Ensure values are within valid range
            r = min(255, max(0, r))
            g = min(255, max(0, g))
            b = min(255, max(0, b))
            
            # Convert to hex color
            color = f'#{r:02x}{g:02x}{b:02x}'
            
            # Draw a line with this color
            self.canvas.create_line(0, i, width, i, fill=color, tags="gradient")
            
        # Bring the content frame to the front
        self.canvas.tag_lower("gradient")
        self.canvas.update_idletasks()
        
    def create_about_content(self):
        """Create the about section with program and developer information"""
        # Main container with padding
        about_container = ttk.Frame(self.content_frame)
        about_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # Program information section
        program_frame = ttk.LabelFrame(about_container, text="معلومات البرنامج")
        program_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Program title
        ttk.Label(
            program_frame, 
            text="نظام إدارة رواتب الموظفين", 
            font=("Arial", 16, "bold")
        ).pack(pady=10)
        
        # Version
        ttk.Label(
            program_frame, 
            text="الإصدار: 1.0.0", 
            font=("Arial", 12)
        ).pack(pady=5)
        
        # Copyright
        ttk.Label(
            program_frame, 
            text=f"حقوق النشر © {datetime.now().year} - جميع الحقوق محفوظة", 
            font=("Arial", 10)
        ).pack(pady=5)
        
        # Description
        description = """
        نظام متكامل لإدارة رواتب الموظفين يتيح إدخال وتعديل وعرض وطباعة 
        بيانات الرواتب الشهرية للموظفين مع إمكانية استيراد البيانات من ملفات Excel.
        """
        ttk.Label(
            program_frame, 
            text=description, 
            font=("Arial", 11),
            wraplength=600,
            justify=tk.RIGHT
        ).pack(pady=10, padx=10)
        
        # Developer information section
        dev_frame = ttk.LabelFrame(about_container, text="معلومات المبرمج")
        dev_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Developer name
        ttk.Label(
            dev_frame, 
            text="علي عاجل خشان المحنة", 
            font=("Arial", 14, "bold")
        ).pack(pady=5)
        
        # Job title
        ttk.Label(
            dev_frame, 
            text="المحاسب المبرمج", 
            font=("Arial", 12, "italic")
        ).pack(pady=2)
        
        # Bio
        bio = """
        مبرمج محترف ومحاسب بخبرة متعددة المجالات، متخصص في تطوير تطبيقات الويب والموبايل باستخدام:
        Python, HTML/CSS/JavaScript, C#/C++
        """
        ttk.Label(
            dev_frame, 
            text=bio, 
            font=("Arial", 11),
            wraplength=600,
            justify=tk.RIGHT
        ).pack(pady=5, padx=10)
        
        # Interests
        interests = """
        الاهتمامات: تطوير تطبيقات الإنترنت والمتصفح، تطبيقات الهواتف الذكية، تصميم واجهات المستخدم UI/UX، أنظمة الحسابات وقواعد البيانات
        """
        ttk.Label(
            dev_frame, 
            text=interests, 
            font=("Arial", 11),
            wraplength=600,
            justify=tk.RIGHT
        ).pack(pady=5, padx=10)
        
        # Contact information
        contact_frame = ttk.Frame(dev_frame)
        contact_frame.pack(fill=tk.X, pady=10, padx=10)
        
        ttk.Label(
            contact_frame, 
            text="معلومات الاتصال:", 
            font=("Arial", 11, "bold")
        ).pack(anchor=tk.E)
        
        ttk.Label(
            contact_frame, 
            text="رقم الهاتف (واتساب): 07727232639", 
            font=("Arial", 11)
        ).pack(anchor=tk.E)
        
        ttk.Label(
            contact_frame, 
            text="البريد الإلكتروني: <EMAIL>", 
            font=("Arial", 11)
        ).pack(anchor=tk.E)
        
    def create_data_clearing_section(self):
        """Create the data clearing section with year selection and clear button"""
        # Data clearing section
        clear_frame = ttk.LabelFrame(self.content_frame, text="تفريغ ومسح البيانات")
        clear_frame.pack(fill=tk.X, padx=30, pady=20)
        
        # Warning message
        warning_text = """
        تحذير: سيؤدي هذا الإجراء إلى حذف بيانات الرواتب نهائياً ولا يمكن استعادتها.
        يرجى التأكد من عمل نسخة احتياطية قبل المتابعة.
        """
        warning_label = ttk.Label(
            clear_frame, 
            text=warning_text,
            font=("Arial", 11, "bold"),
            foreground="red",
            wraplength=600,
            justify=tk.RIGHT
        )
        warning_label.pack(pady=10, padx=10, fill=tk.X)
        
        # Year selection frame
        year_frame = ttk.Frame(clear_frame)
        year_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Year label
        ttk.Label(
            year_frame, 
            text="اختر السنة:", 
            font=("Arial", 11)
        ).pack(side=tk.RIGHT, padx=5)
        
        # Get available years from database
        current_year = datetime.now().year
        years = [str(year) for year in range(current_year - 10, current_year + 1)]
        years.reverse()  # Most recent years first
        
        # Add "All Years" option
        years.insert(0, "جميع السنوات")
        
        # Year combobox
        self.year_var = tk.StringVar()
        self.year_var.set(years[0])  # Default to "All Years"
        
        year_combo = ttk.Combobox(
            year_frame, 
            textvariable=self.year_var,
            values=years,
            state="readonly",
            width=15
        )
        year_combo.pack(side=tk.RIGHT, padx=5)
        
        # Create a style for the purple button
        style = ttk.Style()
        style.configure("Purple.TButton", foreground="white", background="#8A2BE2")
        
        # Clear button frame (for centering)
        button_frame = ttk.Frame(clear_frame)
        button_frame.pack(pady=15)
        
        # Clear button
        clear_button = ttk.Button(
            button_frame, 
            text="تفريغ ومسح البيانات",
            style="Purple.TButton",
            command=self.confirm_clear_data,
            width=20
        )
        clear_button.pack(pady=5)
        
    def confirm_clear_data(self):
        """Confirm and clear data for the selected year"""
        selected_year = self.year_var.get()
        
        # Determine message based on selection
        if selected_year == "جميع السنوات":
            message = "هل أنت متأكد من رغبتك في حذف بيانات الرواتب لجميع السنوات؟"
        else:
            message = f"هل أنت متأكد من رغبتك في حذف بيانات الرواتب لسنة {selected_year}؟"
            
        # Add warning
        message += "\n\nتحذير: لا يمكن التراجع عن هذا الإجراء!"
        
        # Show confirmation dialog
        confirm = messagebox.askyesno("تأكيد الحذف", message, icon='warning')
        
        if confirm:
            try:
                if selected_year == "جميع السنوات":
                    # Clear all salary records
                    self.db_manager.clear_all_salary_records()
                    success_message = "تم حذف جميع بيانات الرواتب بنجاح"
                else:
                    # Clear salary records for specific year
                    year = int(selected_year)
                    self.db_manager.clear_salary_records_by_year(year)
                    success_message = f"تم حذف بيانات الرواتب لسنة {year} بنجاح"
                
                messagebox.showinfo("تم الحذف", success_message)
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف البيانات: {str(e)}")