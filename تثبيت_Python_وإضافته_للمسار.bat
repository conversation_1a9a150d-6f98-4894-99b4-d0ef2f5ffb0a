@echo off
chcp 65001 > nul
echo تثبيت Python وإضافته إلى مسار النظام...
echo.

REM التحقق من وجود ملف التثبيت
if not exist python-installer.exe (
    echo ملف تثبيت Python غير موجود!
    echo يرجى تنزيل Python من الموقع الرسمي: https://www.python.org/downloads/
    pause
    exit /b
)

REM تشغيل ملف التثبيت مع تفعيل خيار إضافة Python إلى مسار النظام
echo تشغيل ملف التثبيت...
echo يرجى التأكد من تفعيل خيار "Add Python to PATH" أثناء التثبيت
python-installer.exe /passive PrependPath=1

echo.
echo تم الانتهاء من تثبيت Python
echo يرجى إعادة تشغيل الكمبيوتر لتطبيق التغييرات
pause