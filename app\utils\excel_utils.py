#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Excel Utilities for Employee Salary Management System
Handles Excel file operations
"""

import pandas as pd
import os
import datetime

def read_excel_file(file_path):
    """
    Read an Excel file and return a pandas DataFrame
    
    Args:
        file_path (str): Path to the Excel file
        
    Returns:
        pandas.DataFrame: DataFrame containing the Excel data
    """
    try:
        # Read Excel file
        df = pd.read_excel(file_path)
        return df
    except Exception as e:
        print(f"Error reading Excel file: {e}")
        return None
        
def export_to_excel(data, file_path):
    """
    Export data to an Excel file
    
    Args:
        data (list): List of dictionaries containing the data
        file_path (str): Path to save the Excel file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Convert data to DataFrame
        df = pd.DataFrame(data)
        
        # Export to Excel
        df.to_excel(file_path, index=False)
        return True
    except Exception as e:
        print(f"Error exporting to Excel: {e}")
        return False
        
def create_monthly_report(employee_data, salary_records, year, month, file_path):
    """
    Create a monthly report for all employees in Excel format
    
    Args:
        employee_data (list): List of employee dictionaries
        salary_records (list): List of salary record dictionaries
        year (int): Year for the report
        month (int): Month for the report
        file_path (str): Path to save the Excel file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Create a dictionary to map employee IDs to their records
        record_map = {r["employee_id"]: r for r in salary_records if r["year"] == year and r["month"] == month}
        
        # Prepare data for export
        export_data = []
        
        for emp in employee_data:
            record = record_map.get(emp["id"])
            
            if record:
                export_data.append({
                    "EmployName": emp["name"],
                    "JobTitle": emp["job_title"],
                    "Department": emp.get("department_name", ""),
                    "PrimSalaryC": record["prim_salary_c"],
                    "zawjiya": record["zawjiya"],
                    "athfal": record["athfal"],
                    "denger": record["denger"],
                    "MansibC": record["mansib_c"],
                    "shahada": record["shahada"],
                    "nakil": record["nakil"],
                    "handasiya": record["handasiya"],
                    "Arzaak": record["arzaak"],
                    "IncomGC": record["incom_gc"],
                    "Tin": record["tin"],
                    "tokifat": record["tokifat"],
                    "dariba": record["dariba"],
                    "eikari": record["eikari"],
                    "reaya": record["reaya"],
                    "raseem": record["raseem"],
                    "tanfeeth": record["tanfeeth"],
                    "eskan": record["eskan"],
                    "Rasheed": record["rasheed"],
                    "rafidaen": record["rafidaen"],
                    "Eijar": record["eijar"],
                    "AmanatOkra": record["amanat_okra"],
                    "Tout": record["tout"],
                    "Alsafi": record["alsafi"],
                    "Note": record["note"]
                })
                
        # Export to Excel
        return export_to_excel(export_data, file_path)
    except Exception as e:
        print(f"Error creating monthly report: {e}")
        return False
        
def create_employee_yearly_report(employee_data, salary_records, employee_id, year, file_path):
    """
    Create a yearly report for a specific employee in Excel format
    
    Args:
        employee_data (list): List of employee dictionaries
        salary_records (list): List of salary record dictionaries
        employee_id (int): ID of the employee
        year (int): Year for the report
        file_path (str): Path to save the Excel file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get employee data
        employee = None
        for emp in employee_data:
            if emp["id"] == employee_id:
                employee = emp
                break
                
        if not employee:
            print(f"Employee with ID {employee_id} not found")
            return False
            
        # Filter records for this employee and year
        records = [r for r in salary_records if r["employee_id"] == employee_id and r["year"] == year]
        
        # Sort records by month
        records = sorted(records, key=lambda x: x["month"])
        
        # Prepare data for export
        export_data = []
        
        # Arabic month names
        months = [
            "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
            "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
        ]
        
        for record in records:
            month_idx = record["month"] - 1
            month_name = months[month_idx] if 0 <= month_idx < len(months) else str(record["month"])
            
            export_data.append({
                "الشهر": month_name,
                "الراتب الاسمي": record["prim_salary_c"],
                "م. الزوجية": record["zawjiya"],
                "م. الأطفال": record["athfal"],
                "م. الخطورة": record["denger"],
                "المنصب": record["mansib_c"],
                "م. الشهادة": record["shahada"],
                "م. النقل": record["nakil"],
                "م. الهندسية": record["handasiya"],
                "م. المهنية": record["arzaak"],
                "م. الجامعية": record["incom_gc"],
                "مجموع الاستحقاقات": record["tin"],
                "التوقفات التقاعدية": record["tokifat"],
                "الضريبة": record["dariba"],
                "العقار": record["eikari"],
                "الرعاية": record["reaya"],
                "الطابع": record["raseem"],
                "التنفيذ": record["tanfeeth"],
                "الإسكان": record["eskan"],
                "الرشيد": record["rasheed"],
                "الرافدين": record["rafidaen"],
                "الإيجار": record["eijar"],
                "أمانات أخرى": record["amanat_okra"],
                "مجموع الاستقطاعات": record["tout"],
                "الصافي": record["alsafi"],
                "ملاحظات": record["note"]
            })
            
        # Export to Excel
        if export_data:
            # Create a writer with xlsxwriter engine
            writer = pd.ExcelWriter(file_path, engine='xlsxwriter')
            
            # Convert data to DataFrame
            df = pd.DataFrame(export_data)
            
            # Write data to Excel
            df.to_excel(writer, sheet_name='السجل السنوي', index=False)
            
            # Get the xlsxwriter workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['السجل السنوي']
            
            # Add a header with employee information
            header_format = workbook.add_format({
                'bold': True,
                'font_size': 14,
                'align': 'center',
                'valign': 'vcenter'
            })
            
            # Merge cells for the header
            worksheet.merge_range('A1:Z1', f"السجل السنوي للموظف: {employee['name']} - {employee.get('job_title', '')}", header_format)
            worksheet.merge_range('A2:Z2', f"المجموعة: {employee.get('department_name', '')} - سنة {year}", header_format)
            
            # Adjust the starting row for the data
            worksheet.set_column('A:Z', 15)  # Set column width
            
            # Save the workbook
            writer.close()
            
            return True
        else:
            print(f"No records found for employee {employee_id} in year {year}")
            return False
    except Exception as e:
        print(f"Error creating employee yearly report: {e}")
        return False