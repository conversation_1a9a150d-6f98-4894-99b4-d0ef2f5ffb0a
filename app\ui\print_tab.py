#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Print Tab for Employee Salary Management System
Handles printing and exporting reports
"""

import os
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import datetime
import tempfile
import webbrowser
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, landscape
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import arabic_reshaper
from bidi.algorithm import get_display

class PrintTab:
    """Tab for printing and exporting reports"""
    
    def __init__(self, parent, db_manager):
        """Initialize the print tab"""
        self.parent = parent
        self.db_manager = db_manager
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        self.frame.pack(fill=tk.BOTH, expand=True)
        
        # Create form elements
        self.create_print_form()
        
        # Initialize variables
        self.current_employee_id = None
        
        # Register Arabic font for PDF
        self.register_fonts()
        
    def register_fonts(self):
        """Register Arabic fonts for PDF generation"""
        try:
            # Try to register Arial font for Arabic support
            # This assumes Arial is available on the system
            font_path = os.path.join(os.environ['WINDIR'], 'Fonts', 'arial.ttf')
            pdfmetrics.registerFont(TTFont('Arabic', font_path))
        except Exception as e:
            print(f"Could not register Arabic font: {e}")
            # We'll handle this gracefully in the PDF generation
        
    def create_print_form(self):
        """Create the print form"""
        # Main container
        main_frame = ttk.Frame(self.frame)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # Report selection section
        report_frame = ttk.LabelFrame(main_frame, text="اختيار التقرير")
        report_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Report type
        ttk.Label(report_frame, text="نوع التقرير:").grid(row=0, column=1, sticky=tk.E, padx=5, pady=5)
        self.report_type_var = tk.StringVar()
        report_types = [
            "سجل موظف شهري",
            "سجل موظف سنوي",
            "قائمة رواتب شهرية للمجموعة",
            "تقرير إحصائي"
        ]
        self.report_type_combo = ttk.Combobox(
            report_frame, 
            textvariable=self.report_type_var, 
            values=report_types, 
            width=30
        )
        self.report_type_combo.grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.report_type_var.set(report_types[0])
        self.report_type_combo.bind("<<ComboboxSelected>>", self.on_report_type_changed)
        
        # Employee selection
        ttk.Label(report_frame, text="الموظف:").grid(row=1, column=1, sticky=tk.E, padx=5, pady=5)
        self.employee_var = tk.StringVar()
        self.employee_combo = ttk.Combobox(report_frame, textvariable=self.employee_var, width=30)
        self.employee_combo.grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Department selection
        ttk.Label(report_frame, text="المجموعة:").grid(row=2, column=1, sticky=tk.E, padx=5, pady=5)
        self.department_var = tk.StringVar()
        self.department_combo = ttk.Combobox(report_frame, textvariable=self.department_var, width=30)
        self.department_combo.grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        
        # Year selection
        ttk.Label(report_frame, text="السنة:").grid(row=3, column=1, sticky=tk.E, padx=5, pady=5)
        self.year_var = tk.StringVar()
        current_year = datetime.datetime.now().year
        years = [str(year) for year in range(current_year - 5, current_year + 6)]
        self.year_combo = ttk.Combobox(report_frame, textvariable=self.year_var, values=years, width=10)
        self.year_combo.grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        self.year_var.set(str(current_year))
        
        # Month selection
        ttk.Label(report_frame, text="الشهر:").grid(row=4, column=1, sticky=tk.E, padx=5, pady=5)
        self.month_var = tk.StringVar()
        months = [
            "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
            "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
        ]
        self.month_combo = ttk.Combobox(report_frame, textvariable=self.month_var, values=months, width=15)
        self.month_combo.grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        current_month = datetime.datetime.now().month
        self.month_var.set(months[current_month - 1])
        
        # Page size
        ttk.Label(report_frame, text="حجم الصفحة:").grid(row=5, column=1, sticky=tk.E, padx=5, pady=5)
        self.page_size_var = tk.StringVar()
        page_sizes = ["A4", "A3"]
        self.page_size_combo = ttk.Combobox(
            report_frame, 
            textvariable=self.page_size_var, 
            values=page_sizes, 
            width=10
        )
        self.page_size_combo.grid(row=5, column=0, sticky=tk.W, padx=5, pady=5)
        self.page_size_var.set("A4")
        
        # Orientation
        ttk.Label(report_frame, text="اتجاه الصفحة:").grid(row=6, column=1, sticky=tk.E, padx=5, pady=5)
        self.orientation_var = tk.StringVar()
        orientations = ["أفقي", "عمودي"]
        self.orientation_combo = ttk.Combobox(
            report_frame, 
            textvariable=self.orientation_var, 
            values=orientations, 
            width=10
        )
        self.orientation_combo.grid(row=6, column=0, sticky=tk.W, padx=5, pady=5)
        self.orientation_var.set("أفقي")
        
        # Preview frame
        preview_frame = ttk.LabelFrame(main_frame, text="معاينة")
        preview_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Preview message
        self.preview_label = ttk.Label(
            preview_frame, 
            text="اختر نوع التقرير والمعلومات المطلوبة ثم اضغط على زر المعاينة",
            wraplength=400,
            justify=tk.CENTER
        )
        self.preview_label.pack(pady=50)
        
        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X, pady=10)
        
        # Preview button
        ttk.Button(
            buttons_frame, 
            text="معاينة", 
            command=self.preview_report
        ).pack(side=tk.LEFT, padx=5)
        
        # Print button
        ttk.Button(
            buttons_frame, 
            text="طباعة", 
            command=self.print_report
        ).pack(side=tk.LEFT, padx=5)
        
        # Export to PDF button
        ttk.Button(
            buttons_frame, 
            text="تصدير إلى PDF", 
            command=self.export_to_pdf
        ).pack(side=tk.RIGHT, padx=5)
        
        # Export to Excel button
        ttk.Button(
            buttons_frame, 
            text="تصدير إلى Excel", 
            command=self.export_to_excel
        ).pack(side=tk.RIGHT, padx=5)
        
        # Load data
        self.load_departments()
        self.load_employees()
        
    def load_departments(self):
        """Load departments into the combobox"""
        departments = self.db_manager.get_departments()
        department_names = [dept["name"] for dept in departments]
        self.department_combo["values"] = department_names
        
        if department_names:
            self.department_var.set(department_names[0])
            
    def load_employees(self, department_id=None):
        """Load employees into the combobox"""
        employees = self.db_manager.get_employees(department_id)
        employee_names = [f"{emp['name']} ({emp['department_name']})" for emp in employees]
        self.employee_data = {f"{emp['name']} ({emp['department_name']})": emp for emp in employees}
        self.employee_combo["values"] = employee_names
        
        if employee_names:
            self.employee_var.set(employee_names[0])
            
    def on_report_type_changed(self, event):
        """Handle report type change"""
        report_type = self.report_type_var.get()
        
        # Enable/disable controls based on report type
        if report_type == "سجل موظف شهري":
            self.employee_combo.config(state="normal")
            self.department_combo.config(state="disabled")
            self.month_combo.config(state="normal")
            self.year_combo.config(state="normal")
        elif report_type == "سجل موظف سنوي":
            self.employee_combo.config(state="normal")
            self.department_combo.config(state="disabled")
            self.month_combo.config(state="disabled")
            self.year_combo.config(state="normal")
        elif report_type == "قائمة رواتب شهرية للمجموعة":
            self.employee_combo.config(state="disabled")
            self.department_combo.config(state="normal")
            self.month_combo.config(state="normal")
            self.year_combo.config(state="normal")
        elif report_type == "تقرير إحصائي":
            self.employee_combo.config(state="disabled")
            self.department_combo.config(state="normal")
            self.month_combo.config(state="normal")
            self.year_combo.config(state="normal")
            
    def preview_report(self):
        """Preview the selected report"""
        report_type = self.report_type_var.get()
        
        # Update preview message
        self.preview_label.config(
            text=f"معاينة تقرير: {report_type}\n\n"
            f"سيتم إنشاء ملف PDF وعرضه في متصفح الويب عند الضغط على زر الطباعة أو التصدير"
        )
        
    def print_report(self):
        """Print the selected report (via PDF)"""
        # Generate PDF and open it for printing
        pdf_path = self.generate_pdf()
        if pdf_path:
            try:
                webbrowser.open(pdf_path)
                messagebox.showinfo(
                    "طباعة التقرير", 
                    "تم فتح التقرير في متصفح الويب. استخدم خيار الطباعة من المتصفح."
                )
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء فتح ملف PDF:\n{str(e)}")
                
    def export_to_pdf(self):
        """Export the report to PDF"""
        # Ask for save location
        file_path = filedialog.asksaveasfilename(
            defaultextension=".pdf",
            filetypes=[("PDF files", "*.pdf")],
            title="حفظ التقرير كملف PDF"
        )
        
        if not file_path:
            return  # User cancelled
            
        # Generate PDF
        pdf_path = self.generate_pdf(file_path)
        if pdf_path:
            messagebox.showinfo(
                "تصدير إلى PDF", 
                f"تم تصدير التقرير بنجاح إلى:\n{pdf_path}"
            )
            
    def export_to_excel(self):
        """Export the report to Excel"""
        # Ask for save location
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx")],
            title="حفظ التقرير كملف Excel"
        )
        
        if not file_path:
            return  # User cancelled
            
        try:
            # Get report data
            data = self.get_report_data()
            
            # Export to Excel
            import pandas as pd
            
            df = pd.DataFrame(data["data"])
            df.to_excel(file_path, index=False)
            
            messagebox.showinfo(
                "تصدير إلى Excel", 
                f"تم تصدير التقرير بنجاح إلى:\n{file_path}"
            )
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء تصدير التقرير إلى Excel:\n{str(e)}")
            
    def generate_pdf(self, output_path=None):
        """Generate a PDF report"""
        try:
            # If no output path is provided, create a temporary file
            if not output_path:
                with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
                    output_path = temp_file.name
                    
            # Get page size
            page_size_name = self.page_size_var.get()
            page_size = A4  # Default
            if page_size_name == "A3":
                from reportlab.lib.pagesizes import A3
                page_size = A3
                
            # Get orientation
            orientation = self.orientation_var.get()
            if orientation == "أفقي":
                page_size = landscape(page_size)
                
            # Create PDF document with RTL support
            doc = SimpleDocTemplate(
                output_path,
                pagesize=page_size,
                rightMargin=30,
                leftMargin=30,
                topMargin=30,
                bottomMargin=30
            )
            
            # Get report data
            report_data = self.get_report_data()
            
            # Create content
            content = []
            
            # Add title
            styles = getSampleStyleSheet()
            
            # Create Arabic style
            arabic_style = ParagraphStyle(
                'Arabic',
                parent=styles['Title'],
                fontName='Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
                alignment=1,  # Center alignment
                fontSize=16
            )
            
            # Create subtitle style
            subtitle_style = ParagraphStyle(
                'Subtitle',
                parent=styles['Normal'],
                fontName='Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica',
                alignment=1,  # Center alignment
                fontSize=12,
                spaceAfter=20
            )
            
            # Format title for Arabic
            title_text = report_data["title"]
            try:
                # Reshape Arabic text for proper display
                reshaped_text = arabic_reshaper.reshape(title_text)
                bidi_text = get_display(reshaped_text)
                title_text = bidi_text
            except:
                # If reshaping fails, use the original text
                pass
                
            title = Paragraph(title_text, arabic_style)
            content.append(title)
            content.append(Spacer(1, 10))
            
            # Add subtitle with developer info
            subtitle_text = "تطوير: المحاسب المبرمج علي عاجل خشان المحنة"
            try:
                reshaped_subtitle = arabic_reshaper.reshape(subtitle_text)
                bidi_subtitle = get_display(reshaped_subtitle)
                subtitle_text = bidi_subtitle
            except:
                pass
                
            subtitle = Paragraph(subtitle_text, subtitle_style)
            content.append(subtitle)
            
            # Add table
            table_data = report_data["table_data"]
            if table_data:
                # Adjust table to fit page width
                available_width = doc.width
                col_count = len(table_data[0]) if table_data else 0
                
                if col_count > 0:
                    # Calculate column widths based on content type
                    col_widths = []
                    for i in range(col_count):
                        # Make first column (labels) wider
                        if i == 0:
                            col_widths.append(available_width * 0.3)
                        # Make last column (notes) wider if it exists
                        elif i == col_count - 1 and "ملاحظات" in str(table_data[0][i]):
                            col_widths.append(available_width * 0.3)
                        else:
                            col_widths.append(available_width * 0.4 / (col_count - 2))
                    
                    table = Table(table_data, colWidths=col_widths)
                else:
                    table = Table(table_data)
                
                # Style the table
                style = TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
                    ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Arabic' if 'Arabic' in pdfmetrics.getRegisteredFontNames() else 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ])
                
                # Add row coloring for readability
                for i in range(1, len(table_data)):
                    if i % 2 == 0:
                        style.add('BACKGROUND', (0, i), (-1, i), colors.lightgrey)
                    
                    # Add special styling for specific rows
                    if "الاستحقاقات" in str(table_data[i][0]):
                        style.add('BACKGROUND', (0, i), (-1, i), colors.lightgreen)
                        style.add('FONTNAME', (0, i), (-1, i), 'Helvetica-Bold')
                    elif "الاستقطاعات" in str(table_data[i][0]):
                        style.add('BACKGROUND', (0, i), (-1, i), colors.lightcoral)
                        style.add('FONTNAME', (0, i), (-1, i), 'Helvetica-Bold')
                    elif "الصافي المدفوع" in str(table_data[i][0]) or "المجموع" in str(table_data[i][0]):
                        style.add('BACKGROUND', (0, i), (-1, i), colors.lightblue)
                        style.add('FONTNAME', (0, i), (-1, i), 'Helvetica-Bold')
                        
                table.setStyle(style)
                content.append(table)
                
            # Add footer with page numbers
            def add_page_number(canvas, doc):
                canvas.saveState()
                canvas.setFont('Helvetica', 9)
                page_num = canvas.getPageNumber()
                text = f"صفحة {page_num}"
                canvas.drawRightString(doc.pagesize[0] - 30, 30, text)
                canvas.drawString(30, 30, f"تاريخ الطباعة: {datetime.datetime.now().strftime('%Y-%m-%d')}")
                canvas.restoreState()
                
            # Build the PDF with page numbers
            doc.build(content, onFirstPage=add_page_number, onLaterPages=add_page_number)
            
            return output_path
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء ملف PDF:\n{str(e)}")
            return None
            
    def get_report_data(self):
        """Get data for the selected report"""
        report_type = self.report_type_var.get()
        
        if report_type == "سجل موظف شهري":
            return self.get_employee_monthly_report()
        elif report_type == "سجل موظف سنوي":
            return self.get_employee_yearly_report()
        elif report_type == "قائمة رواتب شهرية للمجموعة":
            return self.get_department_monthly_report()
        elif report_type == "تقرير إحصائي":
            return self.get_statistical_report()
        else:
            return {"title": "تقرير غير معروف", "table_data": [], "data": []}
            
    def get_employee_monthly_report(self):
        """Get data for employee monthly report"""
        # Get selected employee
        selected = self.employee_var.get()
        if selected not in self.employee_data:
            return {"title": "خطأ: لم يتم اختيار موظف", "table_data": [], "data": []}
            
        employee = self.employee_data[selected]
        employee_id = employee["id"]
        
        # Get year and month
        try:
            year = int(self.year_var.get())
            
            months = [
                "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            ]
            month = months.index(self.month_var.get()) + 1
        except:
            return {"title": "خطأ: السنة أو الشهر غير صحيح", "table_data": [], "data": []}
            
        # Get record
        record = self.db_manager.get_salary_record(employee_id, year, month)
        
        if not record:
            return {
                "title": f"لا يوجد سجل للموظف {employee['name']} لشهر {self.month_var.get()} {year}",
                "table_data": [],
                "data": []
            }
            
        # Create title
        title = f"سجل راتب الموظف: {employee['name']} - {employee['job_title']}\n"
        title += f"المجموعة: {employee['department_name']} - {self.month_var.get()} {year}"
        
        # Create table data
        table_data = [
            ["البند", "القيمة"]
        ]
        
        # Add entitlements
        table_data.append(["الاستحقاقات", ""])
        table_data.append(["الراتب الاسمي", record["prim_salary_c"]])
        table_data.append(["مخصصات الزوجية", record["zawjiya"]])
        table_data.append(["مخصصات الأطفال", record["athfal"]])
        table_data.append(["مخصصات الخطورة", record["denger"]])
        table_data.append(["مخصصات المنصب", record["mansib_c"]])
        table_data.append(["مخصصات الشهادة", record["shahada"]])
        table_data.append(["مخصصات النقل", record["nakil"]])
        table_data.append(["المخصصات الهندسية", record["handasiya"]])
        table_data.append(["المخصصات المهنية", record["arzaak"]])
        table_data.append(["مخصصات الخدمة الجامعية", record["incom_gc"]])
        table_data.append(["مجموع الاستحقاقات", record["tin"]])
        
        # Add deductions
        table_data.append(["الاستقطاعات", ""])
        table_data.append(["التوقفات التقاعدية", record["tokifat"]])
        table_data.append(["الضريبة", record["dariba"]])
        table_data.append(["العقار", record["eikari"]])
        table_data.append(["الرعاية الاجتماعية", record["reaya"]])
        table_data.append(["رسم الطابع", record["raseem"]])
        table_data.append(["التنفيذ", record["tanfeeth"]])
        table_data.append(["الإسكان", record["eskan"]])
        table_data.append(["الرشيد", record["rasheed"]])
        table_data.append(["الرافدين", record["rafidaen"]])
        table_data.append(["الإيجار", record["eijar"]])
        table_data.append(["أمانات أخرى", record["amanat_okra"]])
        table_data.append(["مجموع الاستقطاعات", record["tout"]])
        
        # Add net salary
        table_data.append(["الصافي المدفوع", record["alsafi"]])
        
        # Add note if exists
        if record["note"]:
            table_data.append(["ملاحظات", record["note"]])
            
        # Create data for Excel export
        data = []
        for row in table_data:
            if len(row) == 2:
                data.append({"البند": row[0], "القيمة": row[1]})
                
        return {"title": title, "table_data": table_data, "data": data}
        
    def get_employee_yearly_report(self):
        """Get data for employee yearly report"""
        # Get selected employee
        selected = self.employee_var.get()
        if selected not in self.employee_data:
            return {"title": "خطأ: لم يتم اختيار موظف", "table_data": [], "data": []}
            
        employee = self.employee_data[selected]
        employee_id = employee["id"]
        
        # Get year
        try:
            year = int(self.year_var.get())
        except:
            return {"title": "خطأ: السنة غير صحيحة", "table_data": [], "data": []}
            
        # Get records
        records = self.db_manager.get_employee_salary_records(employee_id, year)
        
        if not records:
            return {
                "title": f"لا توجد سجلات للموظف {employee['name']} لسنة {year}",
                "table_data": [],
                "data": []
            }
            
        # Create title
        title = f"السجل السنوي للموظف: {employee['name']} - {employee['job_title']}\n"
        title += f"المجموعة: {employee['department_name']} - سنة {year}"
        
        # Sort records by month
        records = sorted(records, key=lambda x: x["month"])
        
        # Arabic month names
        months = [
            "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
            "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
        ]
        
        # Create table data
        table_data = [
            ["الشهر", "الراتب الاسمي", "الاستحقاقات", "الاستقطاعات", "الصافي"]
        ]
        
        # Add data for each month
        for record in records:
            month_idx = record["month"] - 1
            month_name = months[month_idx] if 0 <= month_idx < len(months) else str(record["month"])
            
            table_data.append([
                month_name,
                record["prim_salary_c"],
                record["tin"],
                record["tout"],
                record["alsafi"]
            ])
            
        # Add totals row
        total_prim_salary = sum(record["prim_salary_c"] for record in records)
        total_tin = sum(record["tin"] for record in records)
        total_tout = sum(record["tout"] for record in records)
        total_alsafi = sum(record["alsafi"] for record in records)
        
        table_data.append([
            "المجموع",
            total_prim_salary,
            total_tin,
            total_tout,
            total_alsafi
        ])
        
        # Create data for Excel export
        data = []
        for i, row in enumerate(table_data):
            if i > 0:  # Skip header
                data.append({
                    "الشهر": row[0],
                    "الراتب الاسمي": row[1],
                    "الاستحقاقات": row[2],
                    "الاستقطاعات": row[3],
                    "الصافي": row[4]
                })
                
        return {"title": title, "table_data": table_data, "data": data}
        
    def get_department_monthly_report(self):
        """Get data for department monthly report"""
        # Get selected department
        department_name = self.department_var.get()
        
        # Get department ID
        departments = self.db_manager.get_departments()
        department_id = None
        for dept in departments:
            if dept["name"] == department_name:
                department_id = dept["id"]
                break
                
        if not department_id:
            return {"title": "خطأ: المجموعة غير موجودة", "table_data": [], "data": []}
            
        # Get year and month
        try:
            year = int(self.year_var.get())
            
            months = [
                "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            ]
            month = months.index(self.month_var.get()) + 1
        except:
            return {"title": "خطأ: السنة أو الشهر غير صحيح", "table_data": [], "data": []}
            
        # Get employees in this department
        employees = self.db_manager.get_employees(department_id)
        
        if not employees:
            return {
                "title": f"لا يوجد موظفين في المجموعة {department_name}",
                "table_data": [],
                "data": []
            }
            
        # Create title
        title = f"قائمة رواتب المجموعة: {department_name}\n"
        title += f"الشهر: {self.month_var.get()} {year}"
        
        # Create table data
        table_data = [
            ["اسم الموظف", "العنوان الوظيفي", "الراتب الاسمي", "الاستحقاقات", "الاستقطاعات", "الصافي"]
        ]
        
        # Add data for each employee
        total_prim_salary = 0
        total_tin = 0
        total_tout = 0
        total_alsafi = 0
        
        data = []  # For Excel export
        
        for employee in employees:
            record = self.db_manager.get_salary_record(employee["id"], year, month)
            
            if record:
                table_data.append([
                    employee["name"],
                    employee["job_title"],
                    record["prim_salary_c"],
                    record["tin"],
                    record["tout"],
                    record["alsafi"]
                ])
                
                total_prim_salary += record["prim_salary_c"]
                total_tin += record["tin"]
                total_tout += record["tout"]
                total_alsafi += record["alsafi"]
                
                data.append({
                    "اسم الموظف": employee["name"],
                    "العنوان الوظيفي": employee["job_title"],
                    "الراتب الاسمي": record["prim_salary_c"],
                    "الاستحقاقات": record["tin"],
                    "الاستقطاعات": record["tout"],
                    "الصافي": record["alsafi"]
                })
                
        # Add totals row
        table_data.append([
            "المجموع",
            "",
            total_prim_salary,
            total_tin,
            total_tout,
            total_alsafi
        ])
        
        return {"title": title, "table_data": table_data, "data": data}
        
    def get_statistical_report(self):
        """Get data for statistical report"""
        # Get selected department
        department_name = self.department_var.get()
        
        # Get department ID
        departments = self.db_manager.get_departments()
        department_id = None
        for dept in departments:
            if dept["name"] == department_name:
                department_id = dept["id"]
                break
                
        if not department_id:
            return {"title": "خطأ: المجموعة غير موجودة", "table_data": [], "data": []}
            
        # Get year and month
        try:
            year = int(self.year_var.get())
            
            months = [
                "كانون الثاني", "شباط", "آذار", "نيسان", "أيار", "حزيران",
                "تموز", "آب", "أيلول", "تشرين الأول", "تشرين الثاني", "كانون الأول"
            ]
            month = months.index(self.month_var.get()) + 1
        except:
            return {"title": "خطأ: السنة أو الشهر غير صحيح", "table_data": [], "data": []}
            
        # Get employees in this department
        employees = self.db_manager.get_employees(department_id)
        
        if not employees:
            return {
                "title": f"لا يوجد موظفين في المجموعة {department_name}",
                "table_data": [],
                "data": []
            }
            
        # Create title
        title = f"تقرير إحصائي للمجموعة: {department_name}\n"
        title += f"الشهر: {self.month_var.get()} {year}"
        
        # Create table data
        table_data = [
            ["البند", "المجموع", "المتوسط", "الحد الأدنى", "الحد الأعلى"]
        ]
        
        # Collect data
        prim_salary_values = []
        tin_values = []
        tout_values = []
        alsafi_values = []
        
        for employee in employees:
            record = self.db_manager.get_salary_record(employee["id"], year, month)
            
            if record:
                prim_salary_values.append(record["prim_salary_c"])
                tin_values.append(record["tin"])
                tout_values.append(record["tout"])
                alsafi_values.append(record["alsafi"])
                
        # Calculate statistics
        if prim_salary_values:
            # Salary statistics
            total_prim_salary = sum(prim_salary_values)
            avg_prim_salary = total_prim_salary / len(prim_salary_values)
            min_prim_salary = min(prim_salary_values)
            max_prim_salary = max(prim_salary_values)
            
            table_data.append([
                "الراتب الاسمي",
                total_prim_salary,
                round(avg_prim_salary, 2),
                min_prim_salary,
                max_prim_salary
            ])
            
            # Entitlements statistics
            total_tin = sum(tin_values)
            avg_tin = total_tin / len(tin_values)
            min_tin = min(tin_values)
            max_tin = max(tin_values)
            
            table_data.append([
                "الاستحقاقات",
                total_tin,
                round(avg_tin, 2),
                min_tin,
                max_tin
            ])
            
            # Deductions statistics
            total_tout = sum(tout_values)
            avg_tout = total_tout / len(tout_values)
            min_tout = min(tout_values)
            max_tout = max(tout_values)
            
            table_data.append([
                "الاستقطاعات",
                total_tout,
                round(avg_tout, 2),
                min_tout,
                max_tout
            ])
            
            # Net salary statistics
            total_alsafi = sum(alsafi_values)
            avg_alsafi = total_alsafi / len(alsafi_values)
            min_alsafi = min(alsafi_values)
            max_alsafi = max(alsafi_values)
            
            table_data.append([
                "الصافي",
                total_alsafi,
                round(avg_alsafi, 2),
                min_alsafi,
                max_alsafi
            ])
            
            # Additional statistics
            table_data.append([
                "عدد الموظفين",
                len(employees),
                "",
                "",
                ""
            ])
            
            # Create data for Excel export
            data = [
                {
                    "البند": "الراتب الاسمي",
                    "المجموع": total_prim_salary,
                    "المتوسط": round(avg_prim_salary, 2),
                    "الحد الأدنى": min_prim_salary,
                    "الحد الأعلى": max_prim_salary
                },
                {
                    "البند": "الاستحقاقات",
                    "المجموع": total_tin,
                    "المتوسط": round(avg_tin, 2),
                    "الحد الأدنى": min_tin,
                    "الحد الأعلى": max_tin
                },
                {
                    "البند": "الاستقطاعات",
                    "المجموع": total_tout,
                    "المتوسط": round(avg_tout, 2),
                    "الحد الأدنى": min_tout,
                    "الحد الأعلى": max_tout
                },
                {
                    "البند": "الصافي",
                    "المجموع": total_alsafi,
                    "المتوسط": round(avg_alsafi, 2),
                    "الحد الأدنى": min_alsafi,
                    "الحد الأعلى": max_alsafi
                },
                {
                    "البند": "عدد الموظفين",
                    "المجموع": len(employees),
                    "المتوسط": "",
                    "الحد الأدنى": "",
                    "الحد الأعلى": ""
                }
            ]
            
            return {"title": title, "table_data": table_data, "data": data}
        else:
            return {
                "title": f"لا توجد بيانات للمجموعة {department_name} في {self.month_var.get()} {year}",
                "table_data": [],
                "data": []
            }